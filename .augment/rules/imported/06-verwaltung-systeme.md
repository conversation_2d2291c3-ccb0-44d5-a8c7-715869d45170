---
type: "agent_requested"
---

# Verwaltungs-Systeme

Zentrale Verwaltung von administrativen Funktionen (mig<PERSON><PERSON> von `/kunden/` im Juni 2025).

## Migration und Architektur

### 🔄 Migrationshintergrund (Juni 2025)
- **Grund**: Trennung von administrativen Funktionen (Verwaltung) und kundenspezifischen Funktionen (Kunden)
- **Resultat**: Klarere Struktur und bessere Skalierbarkeit
- **API-Umbenennung**: `api.kunden.stammdaten.*` → `api.verwaltung.kunden.*`
- **API-Umbenennung**: `api.kunden.kontingente.*` → `api.verwaltung.kontingente.*`

### 📁 Neue Struktur
```
verwaltung/
├── kunden/          # Kundenstammdaten-Verwaltung
├── kontingente/     # Kontingente-Verwaltung  
└── mitarbeiter/     # Mitarbeiter-Verwaltung
```

## Kunden-Verwaltung (Stammdaten)

### API-Struktur
- **Basis**: `api.verwaltung.kunden.*`
- **Frontend**: `/verwaltung/kunden/`
- **Convex**: `convex/verwaltung/kunden.ts`

### Kernkonzepte
- **Kunde**: Zentrale Entität mit Stammdaten (Name, Standard-Stundenpreis, Standard-Anfahrtskosten)
- **Standorte**: Mehrere Adressen pro Kunde, einer als Hauptstandort
- **Ansprechpartner**: Mehrere Kontakte pro Kunde, einer als Hauptansprechpartner

### Wichtige Geschäftsregeln
- Maximal ein Hauptstandort und ein Hauptansprechpartner pro Kunde
- Löschschutz: Kunden mit verknüpften Leistungen/Kontingenten können nicht gelöscht werden
- Automatische Hauptkontakt-Zuweisung bei Erstellung

### Hauptfunktionen
```typescript
// API-Funktionen
api.verwaltung.kunden.list()        // Alle Kunden abrufen
api.verwaltung.kunden.get(id)       // Einzelnen Kunden abrufen
api.verwaltung.kunden.create(data)  // Neuen Kunden erstellen
api.verwaltung.kunden.update(data)  // Kunden aktualisieren
api.verwaltung.kunden.remove(id)    // Kunden löschen (mit Schutzprüfung)
```

## Kontingente-Verwaltung

### API-Struktur
- **Basis**: `api.verwaltung.kontingente.*`
- **Frontend**: `/verwaltung/kontingente/`
- **Convex**: `convex/verwaltung/kontingente.ts`

### Kernkonzepte
- **Kontingent**: Definierter Stundenumfang mit Gültigkeitszeitraum
- **Verbrauchte Stunden**: Automatische Aktualisierung durch Leistungserfassung
- **Automatische Deaktivierung**: Täglicher Cronjob deaktiviert abgelaufene Kontingente

### Geschäftsregeln
- Standard-Laufzeit: 90 Tage ab Startdatum
- Enddatum muss nach Startdatum liegen
- Nur aktive Kontingente können für Leistungen verwendet werden

### Hauptfunktionen
```typescript
// API-Funktionen
api.verwaltung.kontingente.list()                    // Alle Kontingente mit Kundendaten
api.verwaltung.kontingente.getByKunde(kundenId)      // Kontingente eines Kunden
api.verwaltung.kontingente.getActiveByKunde(kundenId)// Aktive Kontingente eines Kunden
api.verwaltung.kontingente.create(data)              // Neues Kontingent erstellen
api.verwaltung.kontingente.update(data)              // Kontingent aktualisieren
api.verwaltung.kontingente.remove(id)                // Kontingent löschen
```

## Mitarbeiter-Verwaltung

### API-Struktur
- **Basis**: `api.verwaltung.mitarbeiter.*`
- **Frontend**: `/verwaltung/mitarbeiter/`
- **Convex**: `convex/verwaltung/mitarbeiter.ts`

### Zweck
- **E-Mail-Integration**: Automatische CC-Listen für Dokument-Versand
- **Leistungserfassung**: Zuordnung von Mitarbeitern zu Leistungen
- **Interne Verwaltung**: Zentrale Mitarbeiterdatenbank

### Datenmodell
```typescript
{
  name: string,
  email: string
}
```

### Hauptfunktionen
```typescript
// API-Funktionen
api.verwaltung.mitarbeiter.list()        // Alle Mitarbeiter abrufen
api.verwaltung.mitarbeiter.get(id)       // Einzelnen Mitarbeiter abrufen
api.verwaltung.mitarbeiter.create(data)  // Neuen Mitarbeiter erstellen
api.verwaltung.mitarbeiter.update(data)  // Mitarbeiter aktualisieren
api.verwaltung.mitarbeiter.delete_(id)   // Mitarbeiter löschen
```

## Integration und Abhängigkeiten

### Cross-System-Integration
- **Leistungen**: Referenzieren Kunden und Kontingente aus Verwaltung
- **Lieferscheine**: Verwenden Kundendaten aus Verwaltung
- **Dokumentation**: Integriert Kundenstammdaten für Anzeige
- **Termine**: Verknüpfen mit Kundenstammdaten

### Automatisierungen
- **Kontingent-Updates**: Automatische Synchronisation bei Leistungsänderungen
- **Löschschutz**: Referenzprüfung vor Löschvorgängen
- **Cronjobs**: Automatische Deaktivierung abgelaufener Kontingente

## Frontend-Patterns

### Moderne Seitenstruktur
- **Listen-Seiten**: Übersicht mit Filter und Aktionen
- **Detail-Seiten**: Dedizierte Bearbeitung mit `PageLayout`
- **Formular-Komponenten**: Wiederverwendbare, modulare Formulare

### Typische Routen
```
/verwaltung/kunden/          # Kundenübersicht
/verwaltung/kunden/neu       # Neuen Kunden erstellen
/verwaltung/kunden/:id       # Kunden bearbeiten

/verwaltung/kontingente/     # Kontingente-Übersicht
/verwaltung/mitarbeiter/     # Mitarbeiter-Übersicht
```

## Best Practices

### API-Verwendung
- Verwende immer die neuen `api.verwaltung.*` Pfade
- Nutze TypeScript-Interfaces für Typsicherheit
- Implementiere angemessene Fehlerbehandlung

### Frontend-Entwicklung
- Folge der modularen Komponentenstruktur
- Nutze `PageLayout` für konsistente Seitendarstellung
- Implementiere Ladestate und Fehlerbehandlung

### Datenintegrität
- Prüfe Abhängigkeiten vor Löschvorgängen
- Nutze Transaktionen für atomare Updates
- Validiere Eingaben auf Client- und Server-Seite
