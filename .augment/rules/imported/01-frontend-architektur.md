---
type: "always_apply"
---

# Frontend-Architektur

Gesamtstruktur des React-TypeScript-Frontends mit Navigation und UI-Komponenten.

## 1. Projekt-Gesamtstruktur

Die 8IT-Verwaltungsanwendung ist wie folgt strukturiert:

- [`src/`](mdc:src) - Frontend-Quellcode
  - [`components/`](mdc:src/components) - Wiederverwendbare React-Komponenten
    - [`_auth/`](mdc:src/components/_auth) - Authentifizierungskomponenten
    - [`_shared/`](mdc:src/components/_shared) - Shared UI-Komponenten (Button, Card, Select, Dialog, etc.)
    - [`_sonstiges/`](mdc:src/components/_sonstiges) - Komponenten ohne direkte Seitenzuordnung (z.B. Feedback)
    - [`layout/`](mdc:src/components/layout) - Layout-Komponenten (ProtectedAppLayout, PageLayout)
    - [`navigation/`](mdc:src/components/navigation) - Navigations-Komponenten
    - [`erstellung/`](mdc:src/components/erstellung) - Komponenten für Erstellungsseiten
      - [`leistung/`](mdc:src/components/erstellung/leistung) - Komponenten für Leistungserfassung
      - [`uebersicht/`](mdc:src/components/erstellung/uebersicht) - Komponenten für Übersichtserstellung
      - [`lieferschein/`](mdc:src/components/erstellung/lieferschein) - Komponenten für Lieferscheine
    - [`kunden/`](mdc:src/components/kunden) - Komponenten für Kundenseiten
      - [`doku/`](mdc:src/components/kunden/doku) - Komponenten für Kundendokumentation
      - [`termine/`](mdc:src/components/kunden/termine) - Komponenten für Terminverwaltung
    - [`system/`](mdc:src/components/system) - Komponenten für Systemverwaltung
      - [`feedback/`](mdc:src/components/system/feedback) - Komponenten für Feedback-Verwaltung
      - [`standards/`](mdc:src/components/system/standards) - Komponenten für Standardeinstellungen
      - [`doku-kategorien/`](mdc:src/components/system/doku-kategorien) - Komponenten für Doku-Kategorien
      - [`email/`](mdc:src/components/system/email) - Komponenten für E-Mail-System
    - [`verwaltung/`](mdc:src/components/verwaltung) - Komponenten für Verwaltungsseiten
      - [`kunden/`](mdc:src/components/verwaltung/kunden) - Komponenten für Kunden-Verwaltung (Stammdaten)
      - [`kontingente/`](mdc:src/components/verwaltung/kontingente) - Komponenten für Kontingente-Verwaltung
      - [`mitarbeiter/`](mdc:src/components/verwaltung/mitarbeiter) - Komponenten für Mitarbeiter-Verwaltung
  - [`lib/`](mdc:src/lib) - Hilfsbibliotheken und Utilities
    - [`utils/`](mdc:src/lib/utils) - Utility-Funktionen
      - [`cn.ts`](mdc:src/lib/utils/cn.ts) - Tailwind CSS class merging
      - [`dateUtils.ts`](mdc:src/lib/utils/dateUtils.ts) - Umfassende Datum/Zeit-Utilities mit intelligenter Restlaufzeit-Berechnung
      - [`formatUtils.ts`](mdc:src/lib/utils/formatUtils.ts) - Formatierungshilfen
      - [`validationUtils.ts`](mdc:src/lib/utils/validationUtils.ts) - Validierungshilfen
  - [`pages/`](mdc:src/pages) - Seitenkomponenten (nach Hauptnavigationspunkten strukturiert)
    - [`startseite/`](mdc:src/pages/startseite) - Dashboard mit Übersicht über Tages- und Monatsleistungen
    - [`kunden/`](mdc:src/pages/kunden) - Kundenbezogene Seiten
      - [`doku/`](mdc:src/pages/kunden/doku) - Kundenspezifische Dokumentation (Detailseite `[id].tsx` und Formular)
      - [`termine/`](mdc:src/pages/kunden/termine) - Terminverwaltung mit Wiederholungsoptionen
    - [`erstellung/`](mdc:src/pages/erstellung) - Erstellungsbezogene Seiten
      - [`leistung/`](mdc:src/pages/erstellung/leistung) - Erfassung von Leistungen
      - [`uebersicht/`](mdc:src/pages/erstellung/uebersicht) - Erstellung von Übersichten (PDF-Berichte)
      - [`lieferschein/`](mdc:src/pages/erstellung/lieferschein) - Verwaltung von Lieferscheinen
    - [`verwaltung/`](mdc:src/pages/verwaltung) - Verwaltungsbezogene Seiten
      - [`kunden/`](mdc:src/pages/verwaltung/kunden) - Verwaltung der Kundenstammdaten
      - [`kontingente/`](mdc:src/pages/verwaltung/kontingente) - Verwaltung der Kundenkontingente
      - [`mitarbeiter/`](mdc:src/pages/verwaltung/mitarbeiter) - Verwaltung der Mitarbeiter
    - [`system/`](mdc:src/pages/system) - Systemverwaltung
      - [`feedback/`](mdc:src/pages/system/feedback) - Feedback-Verwaltung
      - [`standards/`](mdc:src/pages/system/standards) - Systemweite Standardeinstellungen
      - [`doku-kategorien/`](mdc:src/pages/system/doku-kategorien) - Verwaltung der Dokumentationskategorien
- [`convex/`](mdc:convex) - Backend-Code für Convex-Datenbank
  - [`kunden/`](mdc:convex/kunden) - Kundenbezogene Funktionen
    - [`dokumentation.ts`](mdc:convex/kunden/dokumentation.ts) - API für Kundendokumentationseinträge
    - [`termine.ts`](mdc:convex/kunden/termine.ts) - API für Terminverwaltung
  - [`erstellung/`](mdc:convex/erstellung) - Erstellungsbezogene Funktionen
    - [`leistung.ts`](mdc:convex/erstellung/leistung.ts) - Leistungserfassung-API
    - [`lieferschein.ts`](mdc:convex/erstellung/lieferschein.ts) - Lieferschein-API
  - [`verwaltung/`](mdc:convex/verwaltung) - Verwaltungsbezogene Funktionen
    - [`kunden.ts`](mdc:convex/verwaltung/kunden.ts) - Kundenstammdaten-API
    - [`kontingente.ts`](mdc:convex/verwaltung/kontingente.ts) - Kundenkontingente-API
    - [`mitarbeiter.ts`](mdc:convex/verwaltung/mitarbeiter.ts) - Mitarbeiterverwaltung-API
  - [`system/`](mdc:convex/system) - Systemverwaltungsfunktionen
    - [`standards.ts`](mdc:convex/system/standards.ts) - Standards-API
    - [`storage.ts`](mdc:convex/system/storage.ts) - Dateispeicher-API
    - [`dokuKategorien.ts`](mdc:convex/system/dokuKategorien.ts) - API für Dokumentationskategorien
    - [`dokuKategorienConfig.ts`](mdc:convex/system/dokuKategorienConfig.ts) - Vordefinierte Dokumentationskategorien
  - [`feedback.ts`](mdc:convex/feedback.ts) - Feedback-System API
  - [`schema.ts`](mdc:convex/schema.ts) - Datenbank-Schema-Definition
  - [`auth.config.ts`](mdc:convex/auth.config.ts) - Authentifizierungskonfiguration
  - [`crons.ts`](mdc:convex/crons.ts) - Cronjob-Definitionen

## 2. Technologie-Stack

### Frontend
- **React**: UI-Bibliothek
- **TypeScript**: Typsicheres JavaScript
- **Vite**: Build-Tool und Entwicklungsserver
- **React Router**: Clientseitiges Routing
- **Clerk**: Authentifizierung und Benutzerverwaltung
- **Shadcn UI / Radix UI**: UI-Komponenten mit Dark-Theme
- **Lucide React**: Icon-Bibliothek
- **React PDF**: PDF-Generierung für Übersichten und Lieferscheine
- **Tailwind CSS**: Utility-first CSS-Framework

### Backend
- **Convex**: Backend-as-a-Service mit Datenbank und Serverless-Funktionen
- **Clerk Integration**: Authentifizierung und Autorisierung
- **Resend Integration**: E-Mail-Versand über Convex Resend Component

## 3. Frontend-Detailarchitektur

Dieses Projekt verwendet React mit TypeScript für die Frontend-Entwicklung.

### 3.1. Komponenten-Struktur und Konventionen

- **Dateistruktur**: Komponenten sollten in eigenen Dateien gespeichert werden.
- **Namenskonventionen**:
  - Dateinamen und Komponentennamen in PascalCase (z.B. `Button.tsx`, `PageLayout.tsx`).
  - Props-Interfaces mit dem Suffix `Props` (z.B. `ButtonProps`, `PageLayoutProps`).
- **Imports**:
  - **Ausschließlich** Path-Aliases (`@/`) verwenden - keine relativen Imports
  - Konsistente Import-Struktur für bessere Wartbarkeit
- **Typsicherheit**: Verwendung von TypeScript-Interfaces für Props und State.
- **Performance-Optimierung**:
  - `React.memo` für reine Komponenten die nur von Props abhängen
  - `useCallback` für Event-Handler die an Child-Komponenten weitergegeben werden
  - `useMemo` für teure Berechnungen und Datenverarbeitung
  - Hooks müssen immer in derselben Reihenfolge aufgerufen werden (Rules of Hooks)

### 3.2. Komponenten-Verzeichnisse (`[src/components/](mdc:src/components)`)

```
src/components/
  ├── _auth/        # Authentifizierungskomponenten (SignInButton, SignOutButton)
  ├── _shared/      # Shared UI-Komponenten (Button, Card, Select, Dialog, etc.)
  ├── _sonstiges/   # Komponenten ohne direkte Seitenzuordnung (z.B. Feedback)
  ├── layout/       # Layout-Komponenten (ProtectedAppLayout, PageLayout)
  ├── navigation/   # Navigations-spezifische Komponenten
  ├── erstellung/   # Komponenten für Erstellungsseiten
  │   ├── leistung/   # Komponenten für Leistungserfassung
  │   ├── uebersicht/ # Komponenten für Übersichtserstellung
  │   └── lieferschein/ # Komponenten für Lieferscheine
  ├── kunden/       # Komponenten für Kundenseiten
  │   ├── doku/       # Komponenten für Kundendokumentation
  │   └── termine/    # Komponenten für Terminverwaltung
  ├── system/       # Komponenten für Systemverwaltung
  │   ├── feedback/   # Komponenten für Feedback-Verwaltung
  │   ├── standards/  # Komponenten für Standardeinstellungen
  │   └── doku-kategorien/ # Komponenten für Doku-Kategorien
  └── verwaltung/   # Komponenten für Verwaltungsseiten
      ├── kunden/     # Komponenten für Kunden-Verwaltung (Stammdaten)
      ├── kontingente/ # Komponenten für Kontingente-Verwaltung
      └── mitarbeiter/ # Komponenten für Mitarbeiterverwaltung
```
(Für eine detaillierte Liste der Komponenten pro Verzeichnis, siehe unten.)

### 3.3. Komponenten-Typen (Detailliert)

#### Shared UI-Komponenten (`[src/components/_shared/](mdc:src/components/_shared)`)
Beinhaltet alle wiederverwendbaren UI-Komponenten, die auf mehreren Seiten genutzt werden:
- UI-Elemente wie `Button.tsx`, `Card.tsx`, `Table.tsx`, `Badge.tsx`, `Input.tsx`, `Select.tsx`, `Checkbox.tsx`, etc.
- Dialog-Komponenten wie `Dialog.tsx`, `Tabs.tsx`
- Standardisierte `EmptyState.tsx` Komponente für konsistente Leerzustände
- Shadcn UI Komponenten mit angepasstem Dark-Theme

**Dialog-System**: `AppDialogLayout` ist die zentrale, standardisierte Dialog-Komponente für alle Formular- und Inhalts-Dialoge im System. Sie unterstützt verschiedene Größen (sm, md, lg, xl, 2xl, 3xl, 4xl, 5xl) und bietet konsistente Styling-Patterns.

#### Auth-Komponenten (`[src/components/_auth/](mdc:src/components/_auth)`)
Komponenten für die Authentifizierung:
- `SignInButton.tsx`: Button für den Login
- `SignOutButton.tsx`: Button für den Logout
- `UserButton.tsx`: Button mit Benutzerinformationen

#### Sonstige Komponenten (`[src/components/_sonstiges/](mdc:src/components/_sonstiges)`)
Komponenten, die keiner spezifischen Seite zugeordnet sind:
- Feedback-Komponenten (`feedback/FeedbackButton.tsx`, `feedback/FeedbackDialog.tsx`, `feedback/FeedbackPortal.tsx`)

#### Layout-Komponenten (`[src/components/layout/](mdc:src/components/layout)`)
- **ProtectedAppLayout** ([`ProtectedAppLayout.tsx`](mdc:src/components/layout/ProtectedAppLayout.tsx)): Hauptcontainer für alle Seiten nach dem Login (Navigation, Footer, React Router Outlet).
- **PageLayout** ([`PageLayout.tsx`](mdc:src/components/layout/PageLayout.tsx)): Strukturiert den Inhalt einer Seite (Titel, Subtitel, optionale Aktionen).

#### Navigations-Komponenten (`[src/components/navigation/](mdc:src/components/navigation)`)
- `Navigation.tsx` (Hauptnavigation/Header). Siehe Regel: `navigation.mdc`.

### 3.4. Seitenstruktur und -aufbau (`[src/pages/](mdc:src/pages)`)

#### Verzeichnisstruktur der Seiten
```
src/pages/
  ├── kunden/
  │   ├── doku/           # Kundendokumentation
  │   │   ├── [id].tsx    # Detailansicht der Doku für einen Kunden
  │   │   └── DokuEntryForm.tsx # Formular für Doku-Einträge
  │   └── termine/        # Terminverwaltung
  │       ├── index.tsx   # Terminübersicht mit Kundenfilter
  │       └── [id].tsx    # Kundentermine-Detailansicht
  ├── erstellung/
  │   ├── leistung/       # Leistungserfassung (index.tsx, LeistungForm.tsx, etc.)
  │   ├── uebersicht/     # Übersicht-Erstellung (PDF-Berichte)
  │   └── lieferschein/   # Lieferschein-Verwaltung
  │       ├── index.tsx   # Übersicht aller Lieferscheine
  │       └── [id].tsx    # Detailansicht eines Lieferscheins
  ├── startseite/        # Dashboard mit Übersicht
  ├── verwaltung/
  │   ├── kunden/         # Kundenstammdaten-Verwaltung (index.tsx, [id].tsx, neu.tsx)
  │   ├── kontingente/    # Kundenkontingente-Verwaltung (index.tsx, KontingentForm.tsx, etc.)
  │   └── mitarbeiter/    # Mitarbeiterverwaltung (index.tsx, MitarbeiterForm.tsx, etc.)
  └── system/
      ├── feedback/       # Feedback-Verwaltung (index.tsx)
      ├── standards/      # Systemeinstellungen (index.tsx, StandardsForm.tsx)
      └── doku-kategorien/ # Doku-Kategorien Verwaltung
          ├── index.tsx
          ├── DokuKategorieForm.tsx
          └── DokuKategorienTable.tsx
```

#### Gemeinsame Seitenstruktur
- **Hauptlayout**: Jede Seite verwendet `ProtectedAppLayout`.
- **Seitenlayout**: Innerhalb wird `PageLayout` für Titel, Subtitel und Aktionen genutzt.
- **Header-Aktionen**: Moderne Seiten nutzen `PageLayout` mit `action` prop für Header-Buttons (Zurück, Speichern, Löschen).
- **Modularer Aufbau**: Seiten sind oft in spezialisierte Unterkomponenten aufgeteilt (z.B. Hauptkomponente `index.tsx`, Formular `*Form.tsx` oder `*PageForm.tsx`, Datentabelle `*DataTable.tsx`, Filter `*FilterControls.tsx`, Leerzustand `*EmptyState.tsx`).

#### Typische Seitenmuster

1.  **CRUD-Seiten** (z.B. Kunden, Kontingente, Leistungen, Mitarbeiter):
    
    **Listen-/Übersichtsseiten**:
    - Anzeigen einer Liste/Tabelle von Daten.
    - Filtermöglichkeiten.
    - Button zum Erstellen neuer Einträge (führt zu separater Seite).
    - Bearbeiten-Links für vorhandene Einträge (führen zu separaten Seiten).
    - Aktionen zum Löschen von Einträgen.
    - Anzeige eines Leerzustands, wenn keine Daten vorhanden sind.
    *Beispiel*: `[src/pages/kunden/stammdaten/index.tsx](mdc:src/pages/kunden/stammdaten/index.tsx)`
    
    **CRUD-Detail-/Bearbeitungsseiten** (Moderne Implementierung):
    - Dedizierte Seiten für Erstellen (`/neu`) und Bearbeiten (`/:id`) von Einträgen.
    - Nutzen `PageLayout` mit Header-Aktionen (Zurück, Speichern, ggf. Löschen).
    - Breitere, kompaktere Formulare mit optimierten Grid-Layouts.
    - Bessere Nutzung des verfügbaren Bildschirmplatzes.
    *Beispiele*: 
    - `[src/pages/verwaltung/kunden/neu.tsx](mdc:src/pages/verwaltung/kunden/neu.tsx)`
    - `[src/pages/verwaltung/kunden/[id].tsx](mdc:src/pages/verwaltung/kunden/[id].tsx)`
    - `[src/pages/verwaltung/mitarbeiter/neu.tsx](mdc:src/pages/verwaltung/mitarbeiter/neu.tsx)`
    - `[src/pages/verwaltung/mitarbeiter/[id].tsx](mdc:src/pages/verwaltung/mitarbeiter/[id].tsx)`

2.  **Spezifische Funktionsseiten**:
    - **Übersicht-Erstellung** ([`src/pages/erstellung/uebersicht/index.tsx`](mdc:src/pages/erstellung/uebersicht/index.tsx)): Formular zur Auswahl von Kriterien, Anzeige der ausgewählten Daten, PDF-Generierung. (Siehe Regel: `uebersicht-system.mdc`)
    - **Lieferschein-Verwaltung** ([`src/pages/erstellung/lieferschein/index.tsx`](mdc:src/pages/erstellung/lieferschein/index.tsx)): Erstellung und Verwaltung von Lieferscheinen. (Siehe Regel: `lieferschein-system.mdc`)
    - **Feedback-Verwaltung** ([`src/pages/system/feedback/index.tsx`](mdc:src/pages/system/feedback/index.tsx)): Anzeige und Verwaltung von Feedback-Einträgen. (Siehe Regel: `feedback-system.mdc`)
    - **Systemeinstellungen** ([`src/pages/system/standards/index.tsx`](mdc:src/pages/system/standards/index.tsx)): Formulare zur Anpassung globaler Einstellungen. (Siehe Regel: `standards-system.mdc`)
    - **Doku-Kategorien Verwaltung** ([`src/pages/system/doku-kategorien/index.tsx`](mdc:src/pages/system/doku-kategorien/index.tsx)): Administration der Dokumentationskategorien (vordefiniert vs. eigene, Felddefinitionen). (Siehe Regel: `doku-kategorien-system.mdc`)
    - **Kundendokumentations-Detailseite** ([`src/pages/kunden/doku/[id].tsx`](mdc:src/pages/kunden/doku/[id].tsx)): Anzeige der Stammdaten (Standorte, Ansprechpartner) und aller Dokumentationseinträge für einen Kunden, gruppiert nach Kategorie, in Tabellenform. (Siehe Regel: `doku-kategorien-system.mdc`)

### 3.5. Routing und Navigation
- Das Routing ist in [`src/App.tsx`](mdc:src/App.tsx) definiert.
- Die Hauptnavigation erfolgt über die [`Navigation`](mdc:src/components/navigation/Navigation.tsx)-Komponente.
- Authentifizierung und Schutz von Routen via Clerk. (Siehe Regel: `auth-und-routing.mdc` und `navigation.mdc`)

#### Routing-Patterns
**Listen-Seiten**:
- `/kunden/stammdaten` - Kundenübersicht
- `/verwaltung/mitarbeiter` - Mitarbeiterübersicht

**CRUD-Seiten**:
- `/kunden/stammdaten/neu` - Neuen Kunden erstellen
- `/kunden/stammdaten/:id` - Bestehenden Kunden bearbeiten
- `/verwaltung/mitarbeiter/neu` - Neuen Mitarbeiter erstellen
- `/verwaltung/mitarbeiter/:id` - Bestehenden Mitarbeiter bearbeiten

**Spezielle Seiten**:
- `/kunden/doku/:id` - Kundendokumentation
- `/erstellung/lieferscheine/:id` - Lieferschein-Details

### 3.6. Layout-Verbesserungen und Header-Aktionen

#### PageLayout mit Header-Aktionen
Moderne Seiten nutzen das `action` prop von `PageLayout` für Header-Buttons:

```tsx
const headerActions = (
  <div className="flex items-center gap-2">
    <Link to="/kunden/stammdaten">
      <Button variant="outline" className="gap-2">
        <ArrowLeft className="h-4 w-4" />
        Zurück
      </Button>
    </Link>
    
    <Button
      form="kunden-form"
      type="submit"
      disabled={isSubmitting}
      className="gap-2"
    >
      <Save className="h-4 w-4" />
      {isSubmitting ? "Speichert..." : "Speichern"}
    </Button>
  </div>
);

return (
  <PageLayout 
    title={`Kunde bearbeiten: ${kunde.name}`}
    subtitle="Stammdaten bearbeiten"
    action={headerActions}
  >
    {/* Formular-Inhalt */}
  </PageLayout>
);
```

#### Kompakte, breite Layouts
Neuere Formulare nutzen:
- `max-w-4xl` oder `max-w-6xl` für maximale Breite
- Grid-Layouts für effiziente Raumnutzung:
  - 4-Spalten-Grid für Standorte
  - 3-Spalten-Grid für Ansprechpartner
- Kompaktere Abstände zwischen Formular-Elementen

### 3.7. Beispiel für eine moderne Seitenkomponente (CRUD-Stil)

```tsx
import { useQuery, useMutation } from "convex/react";
import { api } from "@/../convex/_generated/api";
import { useState, useMemo } from "react";
import { PageLayout } from "@/components/layout/PageLayout";
import { Card, CardHeader, CardTitle } from "@/components/_shared/Card";
import { Button } from "@/components/_shared/Button";
import { PlusCircle, X } from "lucide-react";
import type { Id } from "@/../convex/_generated/dataModel";

// Import der untergeordneten Komponenten (Beispiel für 'Kunden')
import { KundenForm } from "./KundenForm";
import { KundenFilterControls } from "./KundenFilterControls";
import { KundenDataTable } from "./KundenDataTable";
import { KundenEmptyState } from "./KundenEmptyState";

export function KundenPage() { // Beispiel
  const daten = useQuery(api.verwaltung.kunden.list) || []; // Anpassbar
  const removeMutation = useMutation(api.verwaltung.kunden.remove); // Anpassbar

  const [showForm, setShowForm] = useState(false);
  const [editingId, setEditingId] = useState<Id<"kunden"> | null>(null); // Typ anpassen
  const [searchTerm, setSearchTerm] = useState("");

  const editingData = useMemo(() =>
    daten.find(d => d._id === editingId) ?? null,
    [editingId, daten]
  );

  const filteredData = useMemo(() =>
    daten.filter(d => d.name.toLowerCase().includes(searchTerm.toLowerCase())), // Filterfeld anpassen
    [daten, searchTerm]
  );

  const handleFormSubmitSuccess = () => {
    setShowForm(false);
    setEditingId(null);
  };

  const handleFormCancel = () => {
    setShowForm(false);
    setEditingId(null);
  };

  const actionButton = (
    <Link to="/kunden/stammdaten/neu">
      <Button size="sm" className="gap-1">
        <PlusCircle className="h-4 w-4" />
        Neu erstellen {/* Text anpassen */}
      </Button>
    </Link>
  );

  return (
    <PageLayout title="Meine Daten" subtitle="Verwaltung meiner Daten" action={actionButton}> {/* Titel anpassen */}
      <Card className="shadow-lg border-0 overflow-hidden">
        <CardHeader className="pb-3 px-5 border-b border-muted">
          <div className="flex flex-wrap items-center justify-between gap-4">
            <CardTitle>Übersicht</CardTitle> {/* Titel anpassen */}
            <KundenFilterControls // Filter-Komponente anpassen
              searchTerm={searchTerm}
              onSearchTermChange={setSearchTerm}
              itemCount={filteredData.length}
            />
          </div>
        </CardHeader>
        {filteredData.length === 0 && !searchTerm ? (
          <KundenEmptyState onAddNew={() => setShowForm(true)} /> // EmptyState-Komponente anpassen
        ) : filteredData.length === 0 && searchTerm ? (
          <KundenEmptyState isFilterResult={true} onResetFilters={() => setSearchTerm("")} />
        ) : (
          <KundenDataTable // DataTable-Komponente anpassen
            kunden={filteredData} // Prop-Name und Daten anpassen
            onEdit={(item) => { // Typ anpassen
              // Navigation zu Bearbeitungsseite
              navigate(`/kunden/stammdaten/${item._id}`);
            }}
            onDelete={async (id) => { // Typ anpassen
              await removeMutation({ id });
            }}
          />
        )}
      </Card>
    </PageLayout>
  );
}
```

## 4. Navigation-System

### Struktur
- **Logo/Marke**: Links in der Kopfzeile, verlinkt zur Startseite
- **Hauptnavigation**: Zentral mit Dropdown-Menüs (Kunden, Erstellung, Verwaltung, System)
- **Benutzerbereich**: Rechts mit Profil, Feedback-Button, Logout

### Navigation-Bereiche
- **Dashboard**: Startseite mit Übersicht über Leistungen und Aktivitäten
- **Kunden**: Stammdaten, Kontingente, Dokumentation
- **Erstellung**: Leistung, Übersicht, Lieferscheine
- **Verwaltung**: Mitarbeiter
- **System**: Feedback, Standards, Doku-Kategorien

### Responsive Design
- **Desktop**: Horizontale Navigation mit Dropdown-Menüs
- **Mobile**: Hamburger-Menü mit Seitennavigation

## 5. Dialog-System

### Zentrale Komponente: `AppDialogLayout`
**Standardisierte** Dialog-Komponente für alle modalen Dialoge im System:

```typescript
interface AppDialogLayoutProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  icon?: ReactNode;
  children: ReactNode;
  footer?: ReactNode;
  footerAction?: {
    label: string;
    onClick: () => void;
    icon?: ReactNode;
    disabled?: boolean;
    loading?: boolean;
  };
  maxWidth?: "sm" | "md" | "lg" | "xl" | "2xl" | "3xl" | "4xl" | "5xl";
}
```

### Design-Merkmale
- Dunkler, halbtransparenter Hintergrund (Overlay)
- Dunkles Theme mit abgerundeten Ecken
- Konsistente Header-/Footer-Struktur
- ESC-Taste und Außenklick zum Schließen
- Vollständige Größenunterstützung (sm bis 5xl)

### Verwendung
- **Formular-Dialoge**: Kunden, Mitarbeiter, Leistungen, Kontingente
- **Feedback-Dialog**: Benutzer-Feedback-System
- **Bestätigungs-Dialoge**: Lösch-Bestätigungen
- **Detail-Dialoge**: Zusätzliche Informationen

### Migration
- `ModalFormDialog` wurde **deprecated** und durch `AppDialogLayout` ersetzt
- Alle neuen Dialoge müssen `AppDialogLayout` verwenden

## 6. Architektur-Prinzipien

- **Modulare Struktur**: Aufteilung in wiederverwendbare Komponenten und klar definierte Seiten.
- **Sicherheit**: Authentifizierung für Routen und Backend-Funktionen (siehe `auth-und-routing.mdc`).
- **Entwickler-Erfahrung**:
  - Klare Pfad-Aliase (`@/`) - **keine relativen Imports**
  - Konsistente Struktur und Typsicherheit
  - Standardisierte Komponenten-Patterns
- **Performance**:
  - React-Performance-Optimierungen (memo, useCallback, useMemo)
  - Konsistente Hook-Reihenfolge (Rules of Hooks)
  - Optimierte Re-Rendering-Strategien
- **Benutzer-Erfahrung**: Modernes UI, responsive Design, Feedback-Mechanismen.
- **Design-System**:
  - Einheitliches Shadcn UI-basiertes Design mit Dark-Theme
  - Standardisierte Dialog-Komponente (`AppDialogLayout`)
  - Konsistente EmptyState-Komponente für Leerzustände
- **Code-Qualität**:
  - Einheitliche Import-Struktur
  - Standardisierte Komponenten-Patterns
  - Performance-optimierte Implementierungen
