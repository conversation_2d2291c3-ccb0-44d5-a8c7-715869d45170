---
type: "always_apply"
---

# React Performance-Optimierung

Richtlinien für Performance-Optimierungen in React-Komponenten zur Verbesserung der Benutzerexperience.

## 1. React.memo für Pure Components

### Verwendung
Verwende `React.memo` für Komponenten, die nur von ihren Props abhängen:

```typescript
import { memo } from "react";

const MyComponent = memo(function MyComponent({ data, onAction }: Props) {
  return (
    <div>
      {/* Component content */}
    </div>
  );
});

MyComponent.displayName = "MyComponent";
```

### Wann verwenden
- **Pure Components**: Komponenten die nur von Props abhängen
- **Häufig re-rendernde Parent-Komponenten**: Wenn Parent oft re-rendert
- **Teure Rendering-Operationen**: Komplexe UI-Strukturen oder Berechnungen
- **Listen-Komponenten**: Besonders bei großen Datenlisten

### Beispiele
- PDF-Komponenten (`PDFDocumentComponent`)
- Listen-Komponenten (`LeistungList`, `KontingentList`)
- Formular-Komponenten mit komplexer Struktur

## 2. useCallback für Event Handlers

### Verwendung
Verwende `useCallback` für Event-Handler die an Child-Komponenten weitergegeben werden:

```typescript
const handleSubmit = useCallback(() => {
  // Handler logic
}, [dependency1, dependency2]);

const handleSelectionChange = useCallback((id: string, selected: boolean) => {
  setSelectedItems(prev => ({
    ...prev,
    items: selected 
      ? [...prev.items, id]
      : prev.items.filter(item => item !== id)
  }));
}, []); // Keine Dependencies da setState mit Funktion
```

### Wann verwenden
- **Event-Handler für Child-Komponenten**: Verhindert unnötige Re-Renders
- **Callback-Props**: Funktionen die als Props weitergegeben werden
- **Komplexe Event-Handler**: Mit aufwendiger Logik

### Dependency-Regeln
- **Externe Werte**: Alle verwendeten Variablen/Funktionen in Dependencies
- **setState mit Funktion**: Keine Dependencies nötig bei `setState(prev => ...)`
- **Stabile Referenzen**: Mutations und andere stabile Refs

## 3. useMemo für Expensive Computations

### Verwendung
Verwende `useMemo` für teure Berechnungen und Datenverarbeitung:

```typescript
const filteredData = useMemo(() => 
  data.filter(item => 
    item.name.toLowerCase().includes(searchTerm.toLowerCase())
  ), [data, searchTerm]
);

const totalAmount = useMemo(() => 
  items.reduce((sum, item) => 
    sum + item.amount * item.quantity, 0
  ), [items]
);
```

### Wann verwenden
- **Datenfilterung**: Große Datenlisten filtern/sortieren
- **Berechnungen**: Summen, Durchschnitte, komplexe Mathematik
- **Datenverarbeitung**: Transformationen, Gruppierungen
- **Objekterstellung**: Komplexe Objekte die oft neu erstellt würden

## 4. Hook-Reihenfolge (Rules of Hooks)

### Kritische Regel
**Alle Hooks müssen immer in derselben Reihenfolge aufgerufen werden!**

```typescript
// ✅ RICHTIG: Alle Hooks vor conditional returns
function MyComponent() {
  const data = useQuery(api.getData);
  const mutation = useMutation(api.updateData);
  
  const handleAction = useCallback(() => {
    // Handler logic
  }, [dependency]);
  
  const processedData = useMemo(() => {
    return data?.map(item => processItem(item));
  }, [data]);
  
  // Conditional returns NACH allen Hooks
  if (!data) {
    return <LoadingState />;
  }
  
  return <div>{/* Component content */}</div>;
}

// ❌ FALSCH: Hooks nach conditional return
function MyComponent() {
  const data = useQuery(api.getData);
  
  if (!data) {
    return <LoadingState />;
  }
  
  // ❌ Diese Hooks werden nicht immer aufgerufen!
  const handleAction = useCallback(() => {}, []);
  const processedData = useMemo(() => {}, [data]);
}
```

### Häufige Fehler
- Hooks nach `if`-Statements oder `return`-Statements
- Hooks in Schleifen oder verschachtelten Funktionen
- Bedingte Hook-Aufrufe

## 5. Performance-Patterns

### Große Komponenten optimieren
```typescript
// Große Page-Komponenten mit vielen State-Updates
const MyPage = memo(function MyPage() {
  // Alle Hooks zuerst
  const [formState, setFormState] = useState(initialState);
  const data = useQuery(api.getData);
  
  // Event-Handler mit useCallback
  const handleFormSubmit = useCallback(() => {
    // Submit logic
  }, [formState.requiredField]);
  
  // Teure Berechnungen mit useMemo
  const filteredData = useMemo(() => 
    data?.filter(item => matchesFilter(item, formState.filter))
  , [data, formState.filter]);
  
  // Conditional returns nach allen Hooks
  if (!data) return <LoadingState />;
  
  return (
    <div>
      <FilterComponent 
        onSubmit={handleFormSubmit} // Stabile Referenz
        data={filteredData} // Memoized data
      />
    </div>
  );
});
```

### Listen-Performance
```typescript
const ItemList = memo(function ItemList({ items, onItemAction }) {
  return (
    <div>
      {items.map(item => (
        <ItemComponent 
          key={item.id} // Wichtig für React's Reconciliation
          item={item}
          onAction={onItemAction} // Parent sollte useCallback verwenden
        />
      ))}
    </div>
  );
});
```

## 6. Wann NICHT optimieren

### Über-Optimierung vermeiden
- **Kleine Komponenten**: Einfache UI-Elemente ohne komplexe Logik
- **Selten re-rendernde Komponenten**: Statische oder selten ändernde Inhalte
- **Primitive Props**: Komponenten die nur Strings/Numbers als Props erhalten
- **Keine Performance-Probleme**: Erst optimieren wenn tatsächliche Probleme auftreten

### Messbare Performance-Probleme
Optimiere nur wenn:
- Sichtbare Verzögerungen bei Benutzerinteraktionen
- Langsame Formular-Updates oder Eingaben
- Ruckelnde Animationen oder Scrolling
- Hohe CPU-Nutzung in DevTools

## 7. Best Practices Zusammenfassung

1. **Hook-Reihenfolge**: Alle Hooks vor conditional returns
2. **React.memo**: Für pure Components mit häufigen Parent-Re-Renders
3. **useCallback**: Für Event-Handler an Child-Komponenten
4. **useMemo**: Für teure Berechnungen und Datenverarbeitung
5. **Dependencies**: Korrekte Dependency-Arrays für alle Hooks
6. **Messbare Optimierung**: Nur bei tatsächlichen Performance-Problemen
7. **Profiling**: React DevTools Profiler zur Identifikation von Problemen

## 8. Debugging Performance

### React DevTools Profiler
- Identifiziere Komponenten mit häufigen Re-Renders
- Messe tatsächliche Render-Zeiten
- Analysiere Ursachen für Re-Renders

### Console-Debugging
```typescript
// Temporäres Debugging für Re-Renders
useEffect(() => {
  console.log('Component re-rendered:', { prop1, prop2 });
});
```

### Performance-Monitoring
- Verwende Browser DevTools Performance Tab
- Achte auf lange Tasks und Frame-Drops
- Teste auf verschiedenen Geräten und Browsern
