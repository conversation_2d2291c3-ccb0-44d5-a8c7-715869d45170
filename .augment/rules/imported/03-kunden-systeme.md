---
type: "agent_requested"
---

# Kunden-Systeme

Zentrale Verwaltung aller **kundenbezogenen** Daten und Funktionen.

## Nach `/verwaltung/` migriert:
- **Kundenstammdaten**: `api.verwaltung.kunden.*` (früher `api.kunden.stammdaten.*`)
- **Kontingente**: `api.verwaltung.kontingente.*` (früher `api.kunden.kontingente.*`)
- **Frontend-Pfade**: `/verwaltung/kunden/` und `/verwaltung/kontingente/`

## Verbleibt bei `/kunden/`:
- **Dokumentation**: `api.kunden.dokumentation.*` - Kundenspezifische Dokumentationseinträge
- **Termine**: `api.kunden.termine.*` - Kundentermine und Wiederholungen

## Dokumentations-System

### Kernkonzepte
- **Kategorien**: Vordefinierte Strukturen (Zugangsdaten, Server-Konfiguration, etc.)
- **Felder**: Typisierte Eingabefelder pro Kategorie (Text, Password, URL, etc.)
- **Extrafelder**: Separate Modal-Anzeige für Detailinformationen
- **Einträge**: Ausgefüllte Datensätze pro Kunde und Kategorie

### API-Struktur
- **Basis**: `api.kunden.dokumentation.*`
- **Integration**: Verwendet Kundenstammdaten aus `api.verwaltung.kunden.*`

### Konfiguration
- Kategorien werden in `convex/system/dokuKategorienConfig.ts` definiert
- Automatische Synchronisation via `initializeDefaults` Mutation
- Numerische IDs für stabile Referenzierung

## Termine-System

### Kernkonzepte
- **Wiederholende Termine**: Intelligente Wiederholungslogik mit kontextspezifischen Optionen
- **Automatische Aktualisierung**: Täglicher Cron-Job für wiederholende Termine

### API-Struktur
- **Basis**: `api.kunden.termine.*`
- **Integration**: Verwendet Kundenstammdaten aus `api.verwaltung.kunden.*`

### Kernkonzepte
- **Kunde**: Zentrale Entität mit Stammdaten (Name, Standard-Stundenpreis, Standard-Anfahrtskosten)
- **Standorte**: Mehrere Adressen pro Kunde, einer als Hauptstandort
- **Ansprechpartner**: Mehrere Kontakte pro Kunde, einer als Hauptansprechpartner

### Wichtige Geschäftsregeln
- Maximal ein Hauptstandort und ein Hauptansprechpartner pro Kunde
- Löschschutz: Kunden mit verknüpften Leistungen/Kontingenten können nicht gelöscht werden
- Automatische Hauptkontakt-Zuweisung bei Erstellung

### Kernkonzepte
- **Kontingent**: Definierter Stundenumfang mit Gültigkeitszeitraum
- **Verbrauchte Stunden**: Automatische Aktualisierung durch Leistungserfassung
- **Automatische Deaktivierung**: Täglicher Cronjob deaktiviert abgelaufene Kontingente

### Geschäftsregeln
- Standard-Laufzeit: 90 Tage ab Startdatum
- Enddatum muss nach Startdatum liegen
- Nur aktive Kontingente können für Leistungen verwendet werden

## Utilities

### Date Utils (`src/lib/utils/dateUtils.ts`)
- `calculateTimeRemaining()` - Intelligente Restlaufzeit mit relativer Vergangenheits-Anzeige
- `formatDate()` - Deutsche Datumsformatierung (DD.MM.YYYY)
- `formatTime()` - Uhrzeitformatierung
- `isPast()` - Vergangenheitsprüfung
- `isToday()` - Heute-Prüfung