import { v } from "convex/values";
import { internal } from "../_generated/api";
import { Doc, Id } from "../_generated/dataModel";
import { mutation, query } from "../_generated/server";

/**
 * Hilfsfunktion zur Stundenberechnung (Aufrunden auf nächste 15 Min)
 */
const calculateHours = (startZeit: number, endZeit: number): number => {
	if (endZeit <= startZeit) return 0;
	const diffInMinutes = (endZeit - startZeit) / (1000 * 60);
	return Math.ceil(diffInMinutes / 15) / 4;
};

/**
 * Definition des Leistungs-Objekts
 */
const leistungObject = v.object({
	_id: v.id("kunden_leistungen"),
	_creationTime: v.number(),
	kundenId: v.id("kunden"),
	mitarbeiterId: v.id("mitarbeiter"),
	kontingentId: v.id("kunden_kontingente"),
	startZeit: v.number(),
	endZeit: v.number(),
	art: v.string(),
	mitAnfahrt: v.boolean(),
	beschreibung: v.string(),
	stunden: v.number(),
	stundenpreis: v.number(),
	anfahrtskosten: v.number(),
	stundenKontingent1: v.optional(v.number()),
	kontingentId2: v.optional(v.id("kunden_kontingente")),
	stundenKontingent2: v.optional(v.number()),
});

/**
 * Definition des erweiterten Leistungs-Objekts mit Namen
 */
const leistungWithNamesObject = v.object({
	_id: v.id("kunden_leistungen"),
	_creationTime: v.number(),
	kundenId: v.id("kunden"),
	mitarbeiterId: v.id("mitarbeiter"),
	kontingentId: v.id("kunden_kontingente"),
	startZeit: v.number(),
	endZeit: v.number(),
	art: v.string(),
	mitAnfahrt: v.boolean(),
	beschreibung: v.string(),
	stunden: v.number(),
	stundenpreis: v.number(),
	anfahrtskosten: v.number(),
	stundenKontingent1: v.optional(v.number()),
	kontingentId2: v.optional(v.id("kunden_kontingente")),
	stundenKontingent2: v.optional(v.number()),
	kundeName: v.string(),
	mitarbeiterName: v.string(),
	datum: v.string(),
	kontingentName: v.string(),
	kontingentName2: v.optional(v.string()),
	inLieferscheinen: v.optional(v.number()), // Anzahl der Lieferscheine, in denen diese Leistung verwendet wird
});

/**
 * Neue Leistung erstellen
 * - Berechnet Stunden & mitAnfahrt automatisch
 * - Verwendet übergebene Preise oder Kunden-Standardpreise
 * - Prüft & aktualisiert Kontingent
 */
export const create = mutation({
	args: {
		kundenId: v.id("kunden"),
		mitarbeiterId: v.id("mitarbeiter"),
		kontingentId: v.id("kunden_kontingente"),
		kontingentId2: v.optional(v.id("kunden_kontingente")),
		startZeit: v.number(),
		endZeit: v.number(),
		art: v.string(), // Erwartet "remote", "vor-Ort", "vor-Ort (free)"
		beschreibung: v.string(),
		stundenpreis: v.optional(v.number()), // Optional, sonst Kunde
		anfahrtskosten: v.optional(v.number()), // Optional, sonst Kunde (nur bei art="vor-Ort")
	},
	returns: v.id("kunden_leistungen"),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		// Zeiten validieren
		if (args.endZeit <= args.startZeit) {
			throw new Error("Endzeit muss nach Startzeit liegen.");
		}

		// Kunde und Kontingent laden
		const kunde = await ctx.db.get(args.kundenId);
		if (!kunde) throw new Error("Kunde nicht gefunden");
		const kontingent1 = await ctx.db.get(args.kontingentId);
		if (!kontingent1) throw new Error("Kontingent nicht gefunden");
		if (!kontingent1.istAktiv)
			throw new Error("Ausgewähltes Kontingent ist nicht aktiv.");

		// Stunden berechnen
		const stunden = calculateHours(args.startZeit, args.endZeit);
		if (stunden <= 0) {
			throw new Error("Berechnete Stunden müssen größer als 0 sein.");
		}

		let stundenKontingent1 = 0;
		let stundenKontingent2 = 0;
		let kontingentId2 = args.kontingentId2;

		const verfuegbareStunden1 =
			kontingent1.stunden - kontingent1.verbrauchteStunden;

		if (stunden <= verfuegbareStunden1) {
			// Alles passt in Kontingent 1
			stundenKontingent1 = stunden;
		} else {
			// Aufteilen auf Kontingent 1 und 2
			stundenKontingent1 = verfuegbareStunden1;
			const restStunden = stunden - stundenKontingent1;

			if (!kontingentId2) {
				throw new Error(
					`Nicht genügend Stunden im Kontingent (${verfuegbareStunden1.toFixed(
						2,
					)}h verfügbar). Bitte wählen Sie ein zweites Kontingent.`,
				);
			}

			const kontingent2 = await ctx.db.get(kontingentId2);
			if (!kontingent2) throw new Error("Zweites Kontingent nicht gefunden");
			if (!kontingent2.istAktiv)
				throw new Error("Zweites Kontingent ist nicht aktiv.");

			const verfuegbareStunden2 =
				kontingent2.stunden - kontingent2.verbrauchteStunden;

			if (restStunden > verfuegbareStunden2) {
				throw new Error(
					`Nicht genügend Stunden im zweiten Kontingent (${verfuegbareStunden2.toFixed(
						2,
					)}h verfügbar).`,
				);
			}
			stundenKontingent2 = restStunden;
		}

		// Preise und Anfahrt bestimmen
		const stundenpreis = args.stundenpreis ?? kunde.stundenpreis;
		const mitAnfahrt = args.art === "vor-Ort";
		const anfahrtskosten = mitAnfahrt
			? (args.anfahrtskosten ?? kunde.anfahrtskosten)
			: 0;

		// Leistung erstellen
		const leistungId = await ctx.db.insert("kunden_leistungen", {
			kundenId: args.kundenId,
			mitarbeiterId: args.mitarbeiterId,
			kontingentId: args.kontingentId,
			startZeit: args.startZeit,
			endZeit: args.endZeit,
			art: args.art,
			mitAnfahrt: mitAnfahrt,
			beschreibung: args.beschreibung,
			stunden: stunden,
			stundenpreis: stundenpreis,
			anfahrtskosten: anfahrtskosten,
			stundenKontingent1: stundenKontingent1,
			kontingentId2: kontingentId2,
			stundenKontingent2:
				stundenKontingent2 > 0 ? stundenKontingent2 : undefined,
		});

		// Kontingente aktualisieren
		await ctx.db.patch(args.kontingentId, {
			verbrauchteStunden: kontingent1.verbrauchteStunden + stundenKontingent1,
		});

		if (kontingentId2 && stundenKontingent2 > 0) {
			const kontingent2 = await ctx.db.get(kontingentId2);
			if (kontingent2) {
				await ctx.db.patch(kontingentId2, {
					verbrauchteStunden:
						kontingent2.verbrauchteStunden + stundenKontingent2,
				});
			}
		}

		return leistungId;
	},
});

/**
 * Alle Leistungen abrufen (Basisobjekt)
 */
export const getAll = query({
	args: {},
	returns: v.array(leistungObject),
	handler: async (ctx) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		return await ctx.db.query("kunden_leistungen").collect();
	},
});

/**
 * Alle Leistungen mit Kunden-, Mitarbeiter- und Kontingentnamen abrufen
 */
export const list = query({
	args: {},
	// Rückgabetyp anpassen an leistungWithNamesObject
	returns: v.array(leistungWithNamesObject),
	handler: async (ctx) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		const leistungen = await ctx.db.query("kunden_leistungen").collect();

		const results = [];
		for (const leistung of leistungen) {
			const kunde = await ctx.db.get(leistung.kundenId);
			const mitarbeiter = await ctx.db.get(leistung.mitarbeiterId);
			const kontingent = await ctx.db.get(leistung.kontingentId); // Kontingent holen
			const kontingent2 = leistung.kontingentId2
				? await ctx.db.get(leistung.kontingentId2)
				: null;

			// Zählen, in wie vielen Lieferscheinen diese Leistung verwendet wird
			const lieferscheinZuordnungen = await ctx.db
				.query("kunden_lieferscheine_zuordnung")
				.withIndex("by_leistung", (q) => q.eq("leistungId", leistung._id))
				.collect();

			// Nur die neuesten Versionen von Lieferscheinen zählen
			const lieferscheinIds = new Set<string>();

			for (const zuordnung of lieferscheinZuordnungen) {
				const lieferschein = await ctx.db.get(zuordnung.lieferscheinId);
				if (!lieferschein) continue;

				// Wenn es ein Original ist, das korrigiert wurde, überspringen
				if (lieferschein.wurdeKorrigiert) continue;

				// Ansonsten hinzufügen (entweder Original ohne Korrektur oder neueste Korrektur)
				lieferscheinIds.add(lieferschein._id.toString());
			}

			results.push({
				...leistung,
				kundeName: kunde?.name || "Unbekannt",
				mitarbeiterName: mitarbeiter?.name || "Unbekannt",
				kontingentName: kontingent?.name || "Unbekannt", // Kontingentname hinzufügen
				kontingentName2: kontingent2?.name,
				datum: new Date(leistung.startZeit).toISOString().split("T")[0],
				inLieferscheinen: lieferscheinIds.size,
			});
		}
		return results;
	},
});

/**
 * Leistung aktualisieren
 * - Passt Stunden/Anfahrt an
 * - Ändert optional Kontingent & passt beide Kontingente an
 */
export const update = mutation({
	args: {
		id: v.id("kunden_leistungen"),
		// Felder, die änderbar sein sollen
		kundenId: v.optional(v.id("kunden")), // Kunde nicht änderbar machen? Ist komplex.
		mitarbeiterId: v.optional(v.id("mitarbeiter")),
		kontingentId: v.optional(v.id("kunden_kontingente")),
		kontingentId2: v.optional(v.id("kunden_kontingente")), // Hinzugefügt
		startZeit: v.optional(v.number()),
		endZeit: v.optional(v.number()),
		art: v.optional(v.string()),
		beschreibung: v.optional(v.string()),
		stundenpreis: v.optional(v.number()),
		anfahrtskosten: v.optional(v.number()),
	},
	returns: v.null(),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		const { id, ...updates } = args;

		// Alte Leistung laden für Vergleiche und alte Kontingent-IDs
		const oldLeistung = await ctx.db.get(id);
		if (!oldLeistung) throw new Error("Leistung nicht gefunden.");

		// --- ALTEN ZUSTAND WIEDERHERSTELLEN ---
		// Alte Kontingente entlasten
		if (oldLeistung.stundenKontingent1 && oldLeistung.stundenKontingent1 > 0) {
			const oldKontingent1 = await ctx.db.get(oldLeistung.kontingentId);
			if (oldKontingent1) {
				await ctx.db.patch(oldLeistung.kontingentId, {
					verbrauchteStunden:
						oldKontingent1.verbrauchteStunden - oldLeistung.stundenKontingent1,
				});
			}
		}
		if (
			oldLeistung.kontingentId2 &&
			oldLeistung.stundenKontingent2 &&
			oldLeistung.stundenKontingent2 > 0
		) {
			const oldKontingent2 = await ctx.db.get(oldLeistung.kontingentId2);
			if (oldKontingent2) {
				await ctx.db.patch(oldLeistung.kontingentId2, {
					verbrauchteStunden:
						oldKontingent2.verbrauchteStunden - oldLeistung.stundenKontingent2,
				});
			}
		}

		// Neue Daten vorbereiten
		const newData: Partial<Doc<"kunden_leistungen">> = { ...updates };

		// 1. Stunden neu berechnen
		const newStartZeit = updates.startZeit ?? oldLeistung.startZeit;
		const newEndZeit = updates.endZeit ?? oldLeistung.endZeit;
		let newStunden = oldLeistung.stunden;

		if (updates.startZeit !== undefined || updates.endZeit !== undefined) {
			if (newEndZeit <= newStartZeit) {
				throw new Error("Endzeit muss nach Startzeit liegen.");
			}
			newStunden = calculateHours(newStartZeit, newEndZeit);
			if (newStunden <= 0) {
				throw new Error("Berechnete Stunden müssen größer als 0 sein.");
			}
			newData.stunden = newStunden;
		}

		// 2. Anfahrt und Preise aktualisieren (wie gehabt)
		const newArt = updates.art ?? oldLeistung.art;
		const kundeIdForDefaults = updates.kundenId ?? oldLeistung.kundenId;
		const kunde = await ctx.db.get(kundeIdForDefaults);

		if (updates.art !== undefined) {
			newData.mitAnfahrt = newArt === "vor-Ort";
			if (newData.mitAnfahrt === false) {
				newData.anfahrtskosten = 0;
			} else {
				newData.anfahrtskosten =
					updates.anfahrtskosten ?? kunde?.anfahrtskosten ?? 0;
			}
		}
		if (updates.stundenpreis !== undefined) {
			newData.stundenpreis = updates.stundenpreis;
		} else if (updates.kundenId !== undefined) {
			newData.stundenpreis = kunde?.stundenpreis ?? 0;
		}

		// 3. Kontingente neu belasten
		const newKontingentId1 = updates.kontingentId ?? oldLeistung.kontingentId;
		const newKontingentId2 = updates.kontingentId2; // Kann null/undefined sein

		const kontingent1 = await ctx.db.get(newKontingentId1);
		if (!kontingent1 || !kontingent1.istAktiv)
			throw new Error("Neues primäres Kontingent ist ungültig oder inaktiv.");

		// Verfügbare Stunden neu abrufen (da wir den alten Zustand schon zurückgesetzt haben)
		const verfuegbareStunden1 =
			kontingent1.stunden - kontingent1.verbrauchteStunden;

		let stundenKontingent1 = 0;
		let stundenKontingent2 = 0;

		if (newStunden <= verfuegbareStunden1) {
			stundenKontingent1 = newStunden;
			newData.kontingentId2 = undefined; // Sicherstellen, dass K2 geleert wird
			newData.stundenKontingent2 = undefined;
		} else {
			stundenKontingent1 = verfuegbareStunden1;
			const restStunden = newStunden - stundenKontingent1;

			if (!newKontingentId2) {
				throw new Error(
					`Nicht genügend Stunden im Kontingent (${verfuegbareStunden1.toFixed(
						2,
					)}h). Bitte zweites Kontingent wählen.`,
				);
			}
			const kontingent2 = await ctx.db.get(newKontingentId2);
			if (!kontingent2 || !kontingent2.istAktiv)
				throw new Error("Zweites Kontingent ist ungültig oder inaktiv.");

			const verfuegbareStunden2 =
				kontingent2.stunden - kontingent2.verbrauchteStunden;
			if (restStunden > verfuegbareStunden2) {
				throw new Error(
					`Nicht genügend Stunden im zweiten Kontingent (${verfuegbareStunden2.toFixed(
						2,
					)}h).`,
				);
			}
			stundenKontingent2 = restStunden;
			newData.kontingentId2 = newKontingentId2;
			newData.stundenKontingent2 = stundenKontingent2;
		}
		newData.stundenKontingent1 = stundenKontingent1;

		// Neue Kontingente belasten
		await ctx.db.patch(newKontingentId1, {
			verbrauchteStunden: kontingent1.verbrauchteStunden + stundenKontingent1,
		});

		if (newKontingentId2 && stundenKontingent2 > 0) {
			const kontingent2 = await ctx.db.get(newKontingentId2); // Neu laden
			if (kontingent2) {
				await ctx.db.patch(newKontingentId2, {
					verbrauchteStunden:
						kontingent2.verbrauchteStunden + stundenKontingent2,
				});
			}
		}

		// Leistung patchen
		await ctx.db.patch(id, newData);
		return null;
	},
});

/**
 * Leistung löschen
 * - Passt das zugehörige Kontingent an
 */
export const remove = mutation({
	args: {
		id: v.id("kunden_leistungen"),
	},
	returns: v.null(),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		// Leistung laden, um Kontingent anzupassen
		const leistung = await ctx.db.get(args.id);
		if (!leistung) {
			console.warn(`Leistung ${args.id} beim Löschen nicht gefunden.`);
			return null; // Oder Fehler werfen?
		}

		// Kontingent anpassen (Stunden zurückgeben)
		if (leistung.stundenKontingent1 && leistung.stundenKontingent1 > 0) {
			const kontingent1 = await ctx.db.get(leistung.kontingentId);
			if (kontingent1) {
				await ctx.db.patch(leistung.kontingentId, {
					verbrauchteStunden: Math.max(
						0,
						kontingent1.verbrauchteStunden - leistung.stundenKontingent1,
					),
				});
			}
		}

		// Zweites Kontingent entlasten
		if (
			leistung.kontingentId2 &&
			leistung.stundenKontingent2 &&
			leistung.stundenKontingent2 > 0
		) {
			const kontingent2 = await ctx.db.get(leistung.kontingentId2);
			if (kontingent2) {
				await ctx.db.patch(leistung.kontingentId2, {
					verbrauchteStunden: Math.max(
						0,
						kontingent2.verbrauchteStunden - leistung.stundenKontingent2,
					),
				});
			}
		}

		// Leistung löschen
		await ctx.db.delete(args.id);
		return null;
	},
});

/**
 * Leistungen nach Kunde abrufen
 */
export const getLeistungenByKunde = query({
	args: {
		kundenId: v.id("kunden"),
	},
	returns: v.array(leistungObject),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		return await ctx.db
			.query("kunden_leistungen")
			.withIndex("by_kunde", (q) => q.eq("kundenId", args.kundenId))
			.collect();
	},
});

/**
 * Leistungen nach Mitarbeiter abrufen
 */
export const getLeistungenByMitarbeiter = query({
	args: {
		mitarbeiterId: v.id("mitarbeiter"),
	},
	returns: v.array(leistungObject),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		return await ctx.db
			.query("kunden_leistungen")
			.withIndex("by_mitarbeiter", (q) =>
				q.eq("mitarbeiterId", args.mitarbeiterId),
			)
			.collect();
	},
});

/**
 * Leistungen nach Kontingent abrufen
 */
export const getLeistungenByKontingent = query({
	args: {
		kontingentId: v.id("kunden_kontingente"),
	},
	returns: v.array(leistungObject),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		return await ctx.db
			.query("kunden_leistungen")
			.withIndex("by_kontingent", (q) =>
				q.eq("kontingentId", args.kontingentId),
			)
			.collect();
	},
});

/**
 * Leistungen nach Kunde abrufen (Alias für getLeistungenByKunde)
 */
export const getByKunde = query({
	args: {
		kundenId: v.id("kunden"),
	},
	returns: v.array(
		v.object({
			_id: v.id("kunden_leistungen"),
			_creationTime: v.number(),
			kundenId: v.id("kunden"),
			mitarbeiterId: v.id("mitarbeiter"),
			kontingentId: v.id("kunden_kontingente"),
			startZeit: v.number(),
			endZeit: v.number(),
			art: v.string(),
			mitAnfahrt: v.boolean(),
			beschreibung: v.string(),
			stunden: v.number(),
			stundenpreis: v.number(),
			anfahrtskosten: v.number(),
			stundenKontingent1: v.optional(v.number()),
			kontingentId2: v.optional(v.id("kunden_kontingente")),
			stundenKontingent2: v.optional(v.number()),
			inLieferscheinen: v.optional(v.number()),
			mitarbeiterName: v.optional(v.string()),
			kontingentName: v.optional(v.string()),
			kontingentName2: v.optional(v.string()),
		}),
	),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		const leistungen = await ctx.db
			.query("kunden_leistungen")
			.withIndex("by_kunde", (q) => q.eq("kundenId", args.kundenId))
			.collect();

		const results = [];
		for (const leistung of leistungen) {
			const mitarbeiter = await ctx.db.get(leistung.mitarbeiterId);
			const kontingent = await ctx.db.get(leistung.kontingentId);
			const kontingent2 = leistung.kontingentId2
				? await ctx.db.get(leistung.kontingentId2)
				: null;

			// Zählen, in wie vielen Lieferscheinen diese Leistung verwendet wird
			const lieferscheinZuordnungen = await ctx.db
				.query("kunden_lieferscheine_zuordnung")
				.withIndex("by_leistung", (q) => q.eq("leistungId", leistung._id))
				.collect();

			// Nur die neuesten Versionen von Lieferscheinen zählen
			const lieferscheinIds = new Set<string>();

			for (const zuordnung of lieferscheinZuordnungen) {
				const lieferschein = await ctx.db.get(zuordnung.lieferscheinId);
				if (!lieferschein) continue;

				// Wenn es ein Original ist, das korrigiert wurde, überspringen
				if (lieferschein.wurdeKorrigiert) continue;

				// Ansonsten hinzufügen (entweder Original ohne Korrektur oder neueste Korrektur)
				lieferscheinIds.add(lieferschein._id.toString());
			}

			results.push({
				...leistung,
				mitarbeiterName: mitarbeiter?.name || "Unbekannt",
				kontingentName: kontingent?.name || "Unbekannt",
				kontingentName2: kontingent2?.name,
				inLieferscheinen: lieferscheinIds.size,
			});
		}

		return results;
	},
});
