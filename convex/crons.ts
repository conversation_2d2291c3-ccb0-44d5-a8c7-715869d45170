import { cronJobs } from "convex/server";
import { internal } from "./_generated/api";

const crons = cronJobs();

// Täglich um 18:00 UTC (20:00 DE): Deaktiviere abgelaufene Kontingente
crons.daily(
	"Deaktiviere abgelaufene Kontingente",
	{ hourUTC: 18, minuteUTC: 0 }, // 18:00 UTC = 20:00 DE
	internal.verwaltung.kontingente.deaktiviereAbgelaufene,
);

// Täglich um 6:00 UTC (8:00 DE): Aktualisiere nächste Wiederholungstermine
crons.daily(
	"Aktualisiere naechste Wiederholungstermine",
	{ hourUTC: 18, minuteUTC: 0 }, // 18:00 UTC = 20:00 DE
	internal.kunden.termine.updateRecurringAppointments,
);

export default crons;
