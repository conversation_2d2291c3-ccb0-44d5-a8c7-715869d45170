import { v } from "convex/values";
import { Id } from "../_generated/dataModel";
import { mutation, query } from "../_generated/server";

/**
 * System Feedback-Objekt-Definition
 */
const feedbackObject = v.object({
	_id: v.id("system_feedback"),
	_creationTime: v.number(),
	userName: v.string(),
	text: v.string(),
	type: v.string(),
	status: v.string(),
});

/**
 * Neues Feedback erstellen
 */
export const create = mutation({
	args: {
		userName: v.string(),
		text: v.string(),
		type: v.string(),
	},
	returns: v.id("system_feedback"),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		return await ctx.db.insert("system_feedback", {
			...args,
			status: "offen",
		});
	},
});

/**
 * Alle Feedback-Einträge abrufen (für Ad<PERSON>-<PERSON>)
 */
export const list = query({
	args: {
		status: v.optional(v.string()),
		type: v.optional(v.string()),
	},
	returns: v.array(feedbackObject),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		// Abfrage mit optionalem Status-Filter und Type-Filter
		let q = ctx.db.query("system_feedback");

		// Wenn beide Filter angegeben sind, verwenden wir den kombinierten Index
		if (args.status !== undefined && args.type !== undefined) {
			return await q
				.withIndex("by_status_and_type", (q) =>
					q.eq("status", args.status!).eq("type", args.type!),
				)
				.collect();
		}

		// Filter nach Status, falls angegeben
		if (args.status !== undefined) {
			return await q
				.withIndex("by_status", (q) => q.eq("status", args.status!))
				.collect();
		}

		// Filter nach Typ, falls angegeben
		if (args.type !== undefined) {
			return await q
				.withIndex("by_type", (q) => q.eq("type", args.type!))
				.collect();
		}

		// Keine Filter angegeben, alle Einträge zurückgeben
		return await q.collect();
	},
});

/**
 * Status eines Feedback-Eintrags aktualisieren
 */
export const updateStatus = mutation({
	args: {
		id: v.id("system_feedback"),
		status: v.string(),
	},
	returns: v.id("system_feedback"),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		await ctx.db.patch(args.id, { status: args.status });
		return args.id;
	},
});

/**
 * Feedback-Eintrag löschen
 */
export const remove = mutation({
	args: {
		id: v.id("system_feedback"),
	},
	returns: v.null(),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		await ctx.db.delete(args.id);
		return null;
	},
});
