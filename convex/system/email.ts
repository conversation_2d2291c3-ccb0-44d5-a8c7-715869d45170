import { Resend, vEmailEvent, vEmailId } from "@convex-dev/resend";
import { v } from "convex/values";
import { components, internal } from "../_generated/api";
import { Id } from "../_generated/dataModel";
import {
	internalAction,
	internalMutation,
	mutation,
} from "../_generated/server";
import { getLieferscheinEmailTemplate } from "../erstellung/lieferscheineConfig";
import { getUebersichtEmailTemplate } from "../erstellung/uebersichtenConfig";

// Initialize Resend component
export const resend: Resend = new Resend(components.resend, {
	onEmailEvent: internal.system.email.handleEmailEvent,
});

/**
 * Handle email events from Resend webhook
 */
export const handleEmailEvent = internalMutation({
	args: {
		id: vEmailId,
		event: vEmailEvent,
	},
	handler: async (ctx, args) => {
		console.log("📧 Email Event:", args.id, args.event.type, args.event.data);

		// Here you could update database records based on email status
		// For example, track delivery status, bounces, complaints, etc.

		// Log important events
		if (args.event.type === "email.delivered") {
			console.log(`✅ Email ${args.id} delivered successfully`);
		} else if (args.event.type === "email.bounced") {
			console.log(`❌ Email ${args.id} bounced:`, args.event.data);
		} else if (args.event.type === "email.complained") {
			console.log(`⚠️ Email ${args.id} marked as spam:`, args.event.data);
		}
	},
});

/**
 * Send a Lieferschein as an email
 */
export const sendLieferschein = mutation({
	args: {
		lieferscheinId: v.id("kunden_lieferscheine"),
		sendToMitarbeiter: v.boolean(),
		sendToKunde: v.boolean(),
		pdfBase64: v.optional(v.string()),
		mainContactName: v.optional(v.string()),
		employeeRecipients: v.optional(
			v.array(
				v.object({
					email: v.string(),
					name: v.string(),
				}),
			),
		),
		customerRecipients: v.optional(
			v.array(
				v.object({
					email: v.string(),
					name: v.string(),
				}),
			),
		),
	},
	handler: async (ctx, args) => {
		// Authenticate the user
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		const {
			lieferscheinId,
			sendToMitarbeiter,
			sendToKunde,
			pdfBase64,
			mainContactName,
			employeeRecipients,
			customerRecipients,
		} = args;

		// Get the lieferschein
		const lieferschein = await ctx.db.get(lieferscheinId);
		if (!lieferschein) {
			throw new Error("Lieferschein nicht gefunden.");
		}

		// Check if the lieferschein is finalized
		if (lieferschein.status !== "fertig") {
			throw new Error("Lieferschein ist nicht finalisiert.");
		}

		// Get the customer
		const kunde = await ctx.db.get(lieferschein.kundenId);
		if (!kunde) {
			throw new Error("Kunde nicht gefunden.");
		}

		// Collect all recipients for logging
		const allRecipients: string[] = [];
		const emailIds: string[] = [];

		// Send emails to mitarbeiter if requested
		if (
			sendToMitarbeiter &&
			employeeRecipients &&
			employeeRecipients.length > 0
		) {
			for (const employee of employeeRecipients) {
				allRecipients.push(`${employee.name} <${employee.email}>`);
				// Use action to send email
				const emailId = await ctx.scheduler.runAfter(
					0,
					internal.system.email.sendLieferscheinEmailAction,
					{
						toEmail: employee.email,
						toName: employee.name,
						templateData: {
							nummer: lieferschein.nummer || `Entwurf (${lieferscheinId})`,
							datum: new Date(lieferschein.erstelltAm).toLocaleDateString(
								"de-DE",
							),
							empfaenger: mainContactName || employee.name,
						},
						lieferscheinId,
						pdfBase64,
					},
				);
				if (emailId) emailIds.push(String(emailId));
			}
		}

		// Send emails to kunde ansprechpartner if requested
		if (sendToKunde && customerRecipients && customerRecipients.length > 0) {
			for (const customer of customerRecipients) {
				allRecipients.push(`${customer.name} <${customer.email}>`);
				// Use action to send email
				const emailId = await ctx.scheduler.runAfter(
					0,
					internal.system.email.sendLieferscheinEmailAction,
					{
						toEmail: customer.email,
						toName: customer.name,
						templateData: {
							nummer: lieferschein.nummer || `Entwurf (${lieferscheinId})`,
							datum: new Date(lieferschein.erstelltAm).toLocaleDateString(
								"de-DE",
							),
							empfaenger: mainContactName || customer.name,
						},
						lieferscheinId,
						pdfBase64,
					},
				);
				if (emailId) emailIds.push(String(emailId));
			}
		}

		// Consolidated log entry
		const lieferscheinNummer =
			lieferschein.nummer || `Entwurf-${lieferscheinId}`;
		const recipientsList =
			allRecipients.length > 0 ? allRecipients.join(", ") : "Keine Empfänger";

		try {
			console.log(
				`[EMAIL] 📧 Lieferschein "${lieferscheinNummer}" | User: ${identity.email} | Recipients: ${recipientsList} | Email IDs: ${emailIds.join(", ")}`,
			);
			return { success: true, emailIds };
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : "Unbekannter Fehler";
			console.error(
				`[EMAIL] ❌ Lieferschein "${lieferscheinNummer}" | User: ${identity.email} | Error: ${errorMessage}`,
			);
			throw error;
		}
	},
});

/**
 * Send a Übersicht as an email
 */
export const sendUebersicht = mutation({
	args: {
		kundeId: v.id("kunden"),
		zeitraum: v.string(),
		pdfBase64: v.string(),
		employeeRecipients: v.optional(
			v.array(
				v.object({
					email: v.string(),
					name: v.string(),
				}),
			),
		),
		customerRecipients: v.optional(
			v.array(
				v.object({
					email: v.string(),
					name: v.string(),
				}),
			),
		),
		mainContactName: v.optional(v.string()),
	},
	handler: async (ctx, args) => {
		// Authenticate the user
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		const {
			kundeId,
			zeitraum,
			pdfBase64,
			employeeRecipients,
			customerRecipients,
			mainContactName,
		} = args;

		// Get the customer
		const kunde = await ctx.db.get(kundeId);
		if (!kunde) {
			throw new Error("Kunde nicht gefunden.");
		}

		// Collect all recipients for logging
		const allRecipients: string[] = [];
		const emailIds: string[] = [];

		// Send emails to employee recipients
		if (employeeRecipients && employeeRecipients.length > 0) {
			for (const employee of employeeRecipients) {
				allRecipients.push(`${employee.name} <${employee.email}>`);
				// Use action to send email
				const emailId = await ctx.scheduler.runAfter(
					0,
					internal.system.email.sendUebersichtEmailAction,
					{
						toEmail: employee.email,
						toName: employee.name,
						templateData: {
							kunde: kunde.name,
							zeitraum,
							empfaenger: mainContactName || employee.name,
						},
						pdfBase64,
					},
				);
				if (emailId) emailIds.push(String(emailId));
			}
		}

		// Send emails to customer recipients
		if (customerRecipients && customerRecipients.length > 0) {
			for (const customer of customerRecipients) {
				allRecipients.push(`${customer.name} <${customer.email}>`);
				// Use action to send email
				const emailId = await ctx.scheduler.runAfter(
					0,
					internal.system.email.sendUebersichtEmailAction,
					{
						toEmail: customer.email,
						toName: customer.name,
						templateData: {
							kunde: kunde.name,
							zeitraum,
							empfaenger: mainContactName || customer.name,
						},
						pdfBase64,
					},
				);
				if (emailId) emailIds.push(String(emailId));
			}
		}

		// Consolidated log entry
		const recipientsList =
			allRecipients.length > 0 ? allRecipients.join(", ") : "Keine Empfänger";

		try {
			console.log(
				`[EMAIL] 📊 Übersicht "${kunde.name} (${zeitraum})" | User: ${identity.email} | Recipients: ${recipientsList} | Email IDs: ${emailIds.join(", ")}`,
			);
			return { success: true, emailIds };
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : "Unbekannter Fehler";
			console.error(
				`[EMAIL] ❌ Übersicht "${kunde.name} (${zeitraum})" | User: ${identity.email} | Error: ${errorMessage}`,
			);
			throw error;
		}
	},
});

/**
 * Action to send Lieferschein email using Resend
 */
export const sendLieferscheinEmailAction = internalAction({
	args: {
		toEmail: v.string(),
		toName: v.string(),
		templateData: v.object({
			nummer: v.string(),
			datum: v.string(),
			empfaenger: v.string(),
		}),
		lieferscheinId: v.id("kunden_lieferscheine"),
		pdfBase64: v.optional(v.string()),
	},
	handler: async (ctx, args) => {
		const { toEmail, toName, templateData, pdfBase64 } = args;

		// Get email template
		const template = getLieferscheinEmailTemplate();

		// Replace placeholders in the template
		let subject = template.subject;
		let body = template.body;

		for (const [key, value] of Object.entries(templateData)) {
			subject = subject.replace(new RegExp(`{{${key}}}`, "g"), value);
			body = body.replace(new RegExp(`{{${key}}}`, "g"), value);
		}

		// Convert plain text body to HTML
		const htmlBody = body.replace(/\n/g, "<br>");

		// Prepare email data
		const fromEmail = process.env.EMAIL_FROM || "<EMAIL>";
		const fromName = process.env.EMAIL_FROM_NAME || "innov8-IT";
		const apiKey = process.env.RESEND_API_KEY;

		if (!apiKey) {
			throw new Error("RESEND_API_KEY environment variable is not set");
		}

		try {
			// Prepare email payload for direct Resend API call
			const emailPayload: any = {
				from: `${fromName} <${fromEmail}>`,
				to: [toEmail],
				subject: subject,
				html: htmlBody,
				text: body,
				reply_to: [fromEmail],
			};

			// Add PDF attachment if provided
			if (pdfBase64) {
				emailPayload.attachments = [
					{
						filename: `LS${templateData.nummer}.pdf`,
						content: pdfBase64,
						content_type: "application/pdf",
					},
				];
			}

			// Send email via direct Resend API call
			const response = await fetch("https://api.resend.com/emails", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
					Authorization: `Bearer ${apiKey}`,
				},
				body: JSON.stringify(emailPayload),
			});

			if (!response.ok) {
				const errorText = await response.text();
				console.error(`Resend API Error (${response.status}):`, errorText);
				throw new Error(
					`E-Mail-Versand fehlgeschlagen: ${response.status} ${response.statusText}`,
				);
			}

			const result = await response.json();
			const emailId = result.id;

			console.log(`📧 Lieferschein email sent with ID: ${emailId}`);
			return emailId;
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : "Unbekannter Fehler";
			console.error(`❌ Failed to send Lieferschein email: ${errorMessage}`);
			throw new Error(`E-Mail-Versand fehlgeschlagen: ${errorMessage}`);
		}
	},
});

/**
 * Action to send Übersicht email using Resend
 */
export const sendUebersichtEmailAction = internalAction({
	args: {
		toEmail: v.string(),
		toName: v.string(),
		templateData: v.object({
			kunde: v.string(),
			zeitraum: v.string(),
			empfaenger: v.string(),
		}),
		pdfBase64: v.string(),
	},
	handler: async (ctx, args) => {
		const { toEmail, toName, templateData, pdfBase64 } = args;

		// Get email template
		const template = getUebersichtEmailTemplate();

		// Replace placeholders in the template
		let subject = template.subject;
		let body = template.body;

		for (const [key, value] of Object.entries(templateData)) {
			subject = subject.replace(new RegExp(`{{${key}}}`, "g"), value);
			body = body.replace(new RegExp(`{{${key}}}`, "g"), value);
		}

		// Convert plain text body to HTML
		const htmlBody = body.replace(/\n/g, "<br>");

		// Prepare email data
		const fromEmail = process.env.EMAIL_FROM || "<EMAIL>";
		const fromName = process.env.EMAIL_FROM_NAME || "innov8-IT";
		const apiKey = process.env.RESEND_API_KEY;

		if (!apiKey) {
			throw new Error("RESEND_API_KEY environment variable is not set");
		}

		try {
			// Prepare email payload for direct Resend API call
			const emailPayload = {
				from: `${fromName} <${fromEmail}>`,
				to: [toEmail],
				subject: subject,
				html: htmlBody,
				text: body,
				reply_to: [fromEmail],
				attachments: [
					{
						filename: `Uebersicht_${templateData.kunde}_${templateData.zeitraum}.pdf`,
						content: pdfBase64,
						content_type: "application/pdf",
					},
				],
			};

			// Send email via direct Resend API call
			const response = await fetch("https://api.resend.com/emails", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
					Authorization: `Bearer ${apiKey}`,
				},
				body: JSON.stringify(emailPayload),
			});

			if (!response.ok) {
				const errorText = await response.text();
				console.error(`Resend API Error (${response.status}):`, errorText);
				throw new Error(
					`E-Mail-Versand fehlgeschlagen: ${response.status} ${response.statusText}`,
				);
			}

			const result = await response.json();
			const emailId = result.id;

			console.log(`📊 Übersicht email sent with ID: ${emailId}`);
			return emailId;
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : "Unbekannter Fehler";
			console.error(`❌ Failed to send Übersicht email: ${errorMessage}`);
			throw new Error(`E-Mail-Versand fehlgeschlagen: ${errorMessage}`);
		}
	},
});

/**
 * Get email status by ID
 */
export const getEmailStatus = mutation({
	args: {
		emailId: v.string(),
	},
	handler: async (ctx, args) => {
		// Authenticate the user
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		try {
			const status = await resend.status(ctx, args.emailId as any);
			return status;
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : "Unbekannter Fehler";
			console.error(`❌ Failed to get email status: ${errorMessage}`);
			throw new Error(`Status-Abfrage fehlgeschlagen: ${errorMessage}`);
		}
	},
});

/**
 * Cancel email by ID
 */
export const cancelEmail = mutation({
	args: {
		emailId: v.string(),
	},
	handler: async (ctx, args) => {
		// Authenticate the user
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		try {
			await resend.cancelEmail(ctx, args.emailId as any);
			console.log(`📧 Email ${args.emailId} cancelled`);
			return { success: true };
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : "Unbekannter Fehler";
			console.error(`❌ Failed to cancel email: ${errorMessage}`);
			throw new Error(`E-Mail-Stornierung fehlgeschlagen: ${errorMessage}`);
		}
	},
});
