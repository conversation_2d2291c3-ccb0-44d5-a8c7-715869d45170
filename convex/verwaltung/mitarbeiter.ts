import { v } from "convex/values";
import { mutation, query } from "../_generated/server";

/**
 * Liste aller Mitarbeiter zurückgeben
 */
export const list = query({
	args: {},
	returns: v.array(
		v.object({
			_id: v.id("mitarbeiter"),
			_creationTime: v.number(),
			name: v.string(),
			email: v.string(),
		}),
	),
	handler: async (ctx) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		return await ctx.db.query("mitarbeiter").collect();
	},
});

/**
 * Einzelnen Mitarbeiter abrufen
 */
export const get = query({
	args: {
		id: v.id("mitarbeiter"),
	},
	returns: v.union(
		v.object({
			_id: v.id("mitarbeiter"),
			_creationTime: v.number(),
			name: v.string(),
			email: v.string(),
		}),
		v.null(),
	),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		return await ctx.db.get(args.id);
	},
});

/**
 * Neuen Mitarbeiter erstellen
 */
export const create = mutation({
	args: {
		name: v.string(),
		email: v.string(),
	},
	returns: v.id("mitarbeiter"),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		return await ctx.db.insert("mitarbeiter", args);
	},
});

/**
 * Mitarbeiter aktualisieren
 */
export const update = mutation({
	args: {
		id: v.id("mitarbeiter"),
		name: v.string(),
		email: v.string(),
	},
	returns: v.id("mitarbeiter"),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		const { id, ...data } = args;
		await ctx.db.patch(id, data);
		return id;
	},
});

/**
 * Mitarbeiter löschen
 */
export const delete_ = mutation({
	args: {
		id: v.id("mitarbeiter"),
	},
	returns: v.null(),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		await ctx.db.delete(args.id);
		return null;
	},
});
