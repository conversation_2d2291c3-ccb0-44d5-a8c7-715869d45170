import { v } from "convex/values";
import { Doc, Id } from "../_generated/dataModel";
import { internalMutation, mutation, query } from "../_generated/server";

// Standard-Gültigkeitsdauer in Tagen
const DEFAULT_VALIDITY_DAYS = 90;
const MILLISECONDS_PER_DAY = 24 * 60 * 60 * 1000;

/**
 * Definition des Kontingent-Objekts
 */
const kontingentObject = v.object({
	_id: v.id("kunden_kontingente"),
	_creationTime: v.number(),
	kundenId: v.id("kunden"),
	name: v.string(),
	stunden: v.number(),
	verbrauchteStunden: v.number(),
	startDatum: v.number(),
	endDatum: v.number(),
	istAktiv: v.boolean(),
});

/**
 * Definition des erweiterten Kontingent-Objekts mit Kundendaten
 */
const kontingentWithKundeObject = v.object({
	_id: v.id("kunden_kontingente"),
	_creationTime: v.number(),
	kundenId: v.id("kunden"),
	name: v.string(),
	stunden: v.number(),
	verbrauchteStunden: v.number(),
	startDatum: v.number(),
	endDatum: v.number(),
	istAktiv: v.boolean(),
	kundeName: v.string(),
	restStunden: v.number(),
});

/**
 * Neues Kontingent erstellen.
 * Setzt Enddatum basierend auf Startdatum (Standard 90 Tage).
 * Wenn startDatum nicht angegeben wird, wird das aktuelle Datum verwendet.
 */
export const create = mutation({
	args: {
		kundenId: v.id("kunden"),
		stunden: v.number(),
		startDatum: v.optional(v.number()),
		name: v.string(),
		endDatum: v.optional(v.number()), // Made optional
	},
	returns: v.id("kunden_kontingente"),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		const startDatumTs = args.startDatum ?? Date.now();
		let endDatumTs = args.endDatum;

		if (endDatumTs === undefined) {
			const startDate = new Date(startDatumTs);
			// Add DEFAULT_VALIDITY_DAYS to startDate to calculate endDatumTs
			endDatumTs = new Date(
				startDate.setDate(startDate.getDate() + DEFAULT_VALIDITY_DAYS),
			).getTime();
		}

		if (endDatumTs <= startDatumTs) {
			throw new Error("Enddatum muss nach dem Startdatum liegen.");
		}

		return await ctx.db.insert("kunden_kontingente", {
			kundenId: args.kundenId,
			name: args.name,
			stunden: args.stunden,
			verbrauchteStunden: 0,
			startDatum: startDatumTs,
			endDatum: endDatumTs,
			istAktiv: true,
		});
	},
});

/**
 * Alle Kontingente mit Kundennamen und verbleibenden Stunden auflisten.
 * Berechnet Reststunden und fügt Kundennamen hinzu.
 */
export const list = query({
	args: {},
	returns: v.array(kontingentWithKundeObject),
	handler: async (ctx) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		const kontingente = await ctx.db.query("kunden_kontingente").collect();

		const results = [];
		for (const kontingent of kontingente) {
			const kunde = await ctx.db.get(kontingent.kundenId);
			const restStunden = kontingent.stunden - kontingent.verbrauchteStunden;

			results.push({
				...kontingent,
				kundeName: kunde?.name || "Unbekannt",
				restStunden,
			});
		}

		return results;
	},
});

/**
 * Kontingent aktualisieren.
 * Erlaubt das Ändern von Stunden, verbrauchten Stunden, Aktivitätsstatus und Startdatum.
 * Wenn Startdatum geändert wird, wird Enddatum automatisch angepasst.
 */
export const update = mutation({
	args: {
		id: v.id("kunden_kontingente"),
		name: v.optional(v.string()),
		stunden: v.optional(v.number()),
		verbrauchteStunden: v.optional(v.number()),
		istAktiv: v.optional(v.boolean()),
		startDatum: v.optional(v.number()),
		endDatum: v.optional(v.number()),
	},
	returns: v.id("kunden_kontingente"),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		const { id, startDatum, endDatum, ...restData } = args;
		const dataToUpdate: Partial<Doc<"kunden_kontingente">> = { ...restData };

		const oldKontingent = await ctx.db.get(id);
		if (!oldKontingent) {
			throw new Error("Kontingent nicht gefunden.");
		}

		let finalStartDatum = oldKontingent.startDatum;
		let finalEndDatum = oldKontingent.endDatum;

		if (startDatum !== undefined) {
			dataToUpdate.startDatum = startDatum;
			finalStartDatum = startDatum;
			// If startDatum is changed and endDatum is not explicitly provided, adjust endDatum to maintain duration
			if (endDatum === undefined) {
				const originalDuration =
					oldKontingent.endDatum - oldKontingent.startDatum;
				dataToUpdate.endDatum = startDatum + originalDuration;
				finalEndDatum = startDatum + originalDuration;
			}
		}

		// If endDatum is explicitly provided, it takes precedence
		if (endDatum !== undefined) {
			dataToUpdate.endDatum = endDatum;
			finalEndDatum = endDatum;
		}

		// Ensure finalEndDatum is after finalStartDatum
		if (
			dataToUpdate.startDatum !== undefined ||
			dataToUpdate.endDatum !== undefined
		) {
			if (finalEndDatum <= finalStartDatum) {
				throw new Error("Enddatum muss nach dem Startdatum liegen.");
			}
		}

		await ctx.db.patch(id, dataToUpdate);
		return id;
	},
});

/**
 * Kontingent löschen.
 */
export const remove = mutation({
	args: {
		id: v.id("kunden_kontingente"),
	},
	returns: v.null(),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		// Hier könnte man prüfen, ob noch Leistungen auf das Kontingent verweisen
		await ctx.db.delete(args.id);
		return null;
	},
});

/**
 * Kontingente nach Kunde abrufen.
 */
export const getByKunde = query({
	args: {
		kundenId: v.id("kunden"),
	},
	returns: v.array(kontingentObject),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		return await ctx.db
			.query("kunden_kontingente")
			.withIndex("by_kunde_and_status", (q) => q.eq("kundenId", args.kundenId))
			.collect();
	},
});

/**
 * Aktive Kontingente eines Kunden abrufen (inklusive Name).
 */
export const getActiveByKunde = query({
	args: {
		kundenId: v.id("kunden"),
	},
	returns: v.array(
		v.object({
			_id: v.id("kunden_kontingente"),
			name: v.string(),
			startDatum: v.number(),
			stunden: v.number(),
			verbrauchteStunden: v.number(),
		}),
	),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		const aktiveKontingente = await ctx.db
			.query("kunden_kontingente")
			.withIndex("by_kunde_and_status", (q) =>
				q.eq("kundenId", args.kundenId).eq("istAktiv", true),
			)
			.collect();

		// Nur relevante Felder zurückgeben
		return aktiveKontingente.map((k) => ({
			_id: k._id,
			name: k.name,
			startDatum: k.startDatum,
			stunden: k.stunden,
			verbrauchteStunden: k.verbrauchteStunden,
		}));
	},
});

/**
 * Alle aktiven Kontingente abrufen.
 */
export const getAllActive = query({
	args: {},
	returns: v.array(kontingentObject),
	handler: async (ctx) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		return await ctx.db
			.query("kunden_kontingente")
			.withIndex("by_status", (q) => q.eq("istAktiv", true))
			.collect();
	},
});

/**
 * Interne Mutation zum Deaktivieren abgelaufener Kontingente.
 * Wird vom Cronjob aufgerufen.
 */
export const deaktiviereAbgelaufene = internalMutation({
	args: {},
	returns: v.null(),
	handler: async (ctx) => {
		const startOfDay = new Date();
		startOfDay.setUTCHours(0, 0, 0, 0);
		const startOfDayTs = startOfDay.getTime();

		const abgelaufeneKontingente = await ctx.db
			.query("kunden_kontingente")
			.withIndex("by_status", (q) => q.eq("istAktiv", true))
			// Filtere Kontingente, deren Enddatum *vor* dem Beginn des heutigen Tages liegt
			.filter((q) => q.lt(q.field("endDatum"), startOfDayTs))
			.collect();

		let count = 0;
		for (const kontingent of abgelaufeneKontingente) {
			await ctx.db.patch(kontingent._id, { istAktiv: false });
			count++;
		}

		// Cron job completed: deactivated expired quotas
		return null;
	},
});
