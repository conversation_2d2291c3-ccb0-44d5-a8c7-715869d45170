import { api } from "@/../convex/_generated/api";
import { Button } from "@/components/_shared/Button";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/_shared/Table";
import { DokuFilterControls } from "@/components/kunden/doku/DokuFilterControls";
import { TermineFilterControls } from "@/components/kunden/termine/TermineFilterControls";
import { PageLayout } from "@/components/layout/PageLayout";
import { StandardDataTable } from "@/components/layout/StandardDataTable";
import {
	calculateTimeRemaining,
	formatDate,
	formatTime,
} from "@/lib/utils/dateUtils";
import { useQuery } from "convex/react";
import { Briefcase, Calendar, Repeat } from "lucide-react";
import { useMemo, useState } from "react";
import { Link } from "react-router-dom";

interface TermineFilter {
	kundeId: string;
	zeitraum: string; // "7", "14", "30", "90", "365"
}

export function KundenTerminePage() {
	const kunden = useQuery(api.verwaltung.kunden.list) || [];
	const [searchTerm, setSearchTerm] = useState("");
	const [filter, setFilter] = useState<TermineFilter>({
		kundeId: "", // Kundenauswahl
		zeitraum: "30", // 7, 14, 30, 90, 365 Tage
	});
	const [showKunden, setShowKunden] = useState(false); // Kunden standardmäßig ausgeblendet

	// Filter customers based on search term
	const filteredKunden = useMemo(() => {
		return kunden.filter((kunde) =>
			kunde.name.toLowerCase().includes(searchTerm.toLowerCase()),
		);
	}, [kunden, searchTerm]);

	// Get upcoming appointments based on filter
	const upcomingTermine =
		useQuery(api.kunden.termine.getUpcoming, {
			days: parseInt(filter.zeitraum),
		}) || [];

	// Filter appointments based on selected customer
	const filteredTermine = useMemo(() => {
		let filtered = upcomingTermine;

		// Filter by selected customer
		if (filter.kundeId) {
			filtered = filtered.filter(
				(termin) => termin.kundenId === filter.kundeId,
			);
		}

		return filtered;
	}, [upcomingTermine, filter.kundeId]);

	// Handler for filter changes
	const handleFilterChange = (field: keyof TermineFilter, value: string) => {
		setFilter((prev) => ({ ...prev, [field]: value }));
	};

	// Handler for resetting all filters
	const resetFilters = () => {
		setFilter({
			kundeId: "",
			zeitraum: "30",
		});
	};

	// Get the main location for each customer
	const getHauptstandortOrt = (kunde: any) => {
		if (!kunde.standorte || kunde.standorte.length === 0) return "-";
		const hauptstandort = kunde.standorte.find((s: any) => s.istHauptstandort);
		return hauptstandort ? hauptstandort.ort : kunde.standorte[0].ort;
	};

	// Get the main contact person for each customer
	const getHauptansprechpartnerName = (kunde: any) => {
		if (!kunde.ansprechpartner || kunde.ansprechpartner.length === 0)
			return "-";
		const hauptansprechpartner = kunde.ansprechpartner.find(
			(ap: any) => ap.istHauptansprechpartner,
		);
		return hauptansprechpartner
			? hauptansprechpartner.name
			: kunde.ansprechpartner[0].name;
	};

	return (
		<PageLayout
			title="Kundentermine"
			subtitle="Termine für alle Kunden verwalten"
			action={
				<Button
					variant="outline"
					size="sm"
					onClick={() => setShowKunden(!showKunden)}
					className="gap-1"
				>
					<Briefcase className="h-4 w-4" />
					{showKunden ? "Kunden ausblenden" : "Kunden anzeigen"}
				</Button>
			}
		>
			<div className="space-y-8">
				{/* Kundenübersicht - nur anzeigen wenn showKunden true ist */}
				{showKunden && (
					<StandardDataTable
						title="Kundenübersicht"
						infoSlot={
							<>
								<Briefcase className="h-3.5 w-3.5 opacity-70" />
								<span>
									{filteredKunden.length}{" "}
									{filteredKunden.length === 1 ? "Kunde" : "Kunden"}
								</span>
							</>
						}
						filterSlot={
							<DokuFilterControls
								searchTerm={searchTerm}
								onSearchTermChange={setSearchTerm}
							/>
						}
					>
						<div className="overflow-x-auto">
							<Table>
								<TableHeader>
									<TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
										<TableHead className="font-medium">Name</TableHead>
										<TableHead className="font-medium">Hauptstandort</TableHead>
										<TableHead className="font-medium">
											Hauptansprechpartner
										</TableHead>
										<TableHead className="w-24 text-center">Aktionen</TableHead>
									</TableRow>
								</TableHeader>
								<TableBody>
									{filteredKunden.length === 0 ? (
										<TableRow>
											<TableCell colSpan={4} className="h-24 text-center">
												{searchTerm
													? `Keine Kunden gefunden für "${searchTerm}"`
													: "Keine Kunden vorhanden"}
											</TableCell>
										</TableRow>
									) : (
										filteredKunden.map((kunde) => (
											<TableRow
												key={kunde._id}
												className="border-b border-gray-800"
											>
												<TableCell className="py-3">
													<div className="flex items-center gap-2">
														<div className="w-7 h-7 rounded-full bg-gray-700 flex items-center justify-center text-gray-300">
															<Briefcase className="h-3.5 w-3.5" />
														</div>
														<span className="font-medium">{kunde.name}</span>
													</div>
												</TableCell>
												<TableCell>{getHauptstandortOrt(kunde)}</TableCell>
												<TableCell>
													{getHauptansprechpartnerName(kunde)}
												</TableCell>
												<TableCell className="text-center">
													<div className="flex justify-center">
														<Link to={`/kunden/termine/${kunde._id}`}>
															<Button
																variant="ghost"
																size="icon"
																className="h-8 w-8 text-gray-400 hover:text-blue-400"
																title="Termine"
															>
																<Calendar className="h-4 w-4" />
																<span className="sr-only">Termine</span>
															</Button>
														</Link>
													</div>
												</TableCell>
											</TableRow>
										))
									)}
								</TableBody>
							</Table>
						</div>
					</StandardDataTable>
				)}

				{/* Termine-Übersicht */}
				<StandardDataTable
					title="Anstehende Termine"
					infoSlot={
						<>
							<Calendar className="h-3.5 w-3.5 opacity-70" />
							<span>
								{filteredTermine.length}{" "}
								{filteredTermine.length === 1 ? "Termin" : "Termine"}
							</span>
						</>
					}
					filterSlot={
						<TermineFilterControls
							filter={filter}
							kunden={kunden}
							onFilterChange={handleFilterChange}
							onResetAllFilters={resetFilters}
						/>
					}
				>
					<div className="overflow-x-auto">
						<Table>
							<TableHeader>
								<TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
									<TableHead className="py-2 px-3 text-xs">Kategorie</TableHead>
									<TableHead className="py-2 px-3 text-xs">Kunde</TableHead>
									<TableHead className="py-2 px-3 text-xs">Titel</TableHead>
									<TableHead className="py-2 px-3 text-xs">Datum</TableHead>
									<TableHead className="py-2 px-3 text-xs">Uhrzeit</TableHead>
									<TableHead className="py-2 px-3 text-xs">
										Restlaufzeit
									</TableHead>
									<TableHead className="w-24 text-center py-2 px-3 text-xs">
										Aktionen
									</TableHead>
								</TableRow>
							</TableHeader>
							<TableBody>
								{filteredTermine.length === 0 ? (
									<TableRow>
										<TableCell colSpan={7} className="h-24 text-center">
											{filter.kundeId
												? `Keine Termine für den ausgewählten Kunden in den nächsten ${filter.zeitraum} Tagen`
												: `Keine Termine in den nächsten ${filter.zeitraum} Tagen`}
										</TableCell>
									</TableRow>
								) : (
									filteredTermine.map((termin) => {
										const timeRemaining = calculateTimeRemaining(
											termin.istWiederholend
												? termin.naechsteWiederholung
												: termin.datum,
											termin.uhrzeit,
										);
										// Status entfernt - wird durch Kategorie ersetzt

										return (
											<TableRow key={termin._id}>
												<TableCell className="py-1.5 px-3 text-xs">
													<span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-500/10 text-blue-400">
														{termin.kategorie}
													</span>
												</TableCell>
												<TableCell className="py-1.5 px-3 text-xs">
													<div className="flex items-center gap-2">
														<div className="w-7 h-7 rounded-full bg-gray-700 flex items-center justify-center text-gray-300">
															<Briefcase className="h-3.5 w-3.5" />
														</div>
														<span className="font-medium">
															{termin.kundeName}
														</span>
													</div>
												</TableCell>
												<TableCell className="py-1.5 px-3 text-xs">
													<div className="flex items-center gap-1">
														{termin.titel}
														{termin.istWiederholend && (
															<Repeat className="h-3 w-3 text-blue-400" />
														)}
													</div>
												</TableCell>
												<TableCell className="py-1.5 px-3 text-xs">
													{termin.istWiederholend
														? formatDate(termin.naechsteWiederholung || "")
														: formatDate(termin.datum || "")}
												</TableCell>
												<TableCell className="py-1.5 px-3 text-xs">
													{formatTime(termin.uhrzeit)}
												</TableCell>
												<TableCell className="py-1.5 px-3 text-xs">
													<span
														className={
															timeRemaining.startsWith("vor")
																? "text-red-400"
																: "text-green-400"
														}
													>
														{timeRemaining}
													</span>
												</TableCell>
												<TableCell className="text-center py-1.5 px-3 text-xs">
													<div className="flex justify-center">
														<Link to={`/kunden/termine/${termin.kundenId}`}>
															<Button
																variant="ghost"
																size="icon"
																className="h-6 w-6 text-gray-400 hover:text-blue-400"
																title="Termine verwalten"
															>
																<Calendar className="h-3 w-3" />
																<span className="sr-only">
																	Termine verwalten
																</span>
															</Button>
														</Link>
													</div>
												</TableCell>
											</TableRow>
										);
									})
								)}
							</TableBody>
						</Table>
					</div>
				</StandardDataTable>
			</div>
		</PageLayout>
	);
}
