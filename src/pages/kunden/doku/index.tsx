import { api } from "@/../convex/_generated/api";
import type { Id } from "@/../convex/_generated/dataModel";
import { Button } from "@/components/_shared/Button";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/_shared/Table";
import { DokuFilterControls } from "@/components/kunden/doku/DokuFilterControls";
import { PageLayout } from "@/components/layout/PageLayout";
import { StandardDataTable } from "@/components/layout/StandardDataTable";
import { useMutation, useQuery } from "convex/react";
import { BookOpen, Briefcase, Search, X } from "lucide-react";
import { useMemo, useState } from "react";
import { Link } from "react-router-dom";

export function KundenDokuPage() {
	const kunden = useQuery(api.verwaltung.kunden.list) || [];
	const [searchTerm, setSearchTerm] = useState("");

	// Filter customers based on search term
	const filteredKunden = useMemo(() => {
		return kunden.filter((kunde) =>
			kunde.name.toLowerCase().includes(searchTerm.toLowerCase()),
		);
	}, [kunden, searchTerm]);

	// Get the main location for each customer
	const getHauptstandortOrt = (kunde: any) => {
		if (!kunde.standorte || kunde.standorte.length === 0) return "-";
		const hauptstandort = kunde.standorte.find((s: any) => s.istHauptstandort);
		return hauptstandort ? hauptstandort.ort : kunde.standorte[0].ort;
	};

	// Get the main contact for each customer
	const getHauptansprechpartnerName = (kunde: any) => {
		if (!kunde.ansprechpartner || kunde.ansprechpartner.length === 0)
			return "-";
		const hauptansprechpartner = kunde.ansprechpartner.find(
			(a: any) => a.istHauptansprechpartner,
		);
		return hauptansprechpartner
			? hauptansprechpartner.name
			: kunde.ansprechpartner[0].name;
	};

	return (
		<PageLayout
			title="Kundendokumentation"
			subtitle="Dokumentation für alle Kunden verwalten"
		>
			<StandardDataTable
				title="Kundenübersicht"
				infoSlot={
					<>
						<Briefcase className="h-3.5 w-3.5 opacity-70" />
						<span>
							{filteredKunden.length}{" "}
							{filteredKunden.length === 1 ? "Kunde" : "Kunden"}
						</span>
					</>
				}
				filterSlot={
					<DokuFilterControls
						searchTerm={searchTerm}
						onSearchTermChange={setSearchTerm}
					/>
				}
			>
				<div className="overflow-x-auto">
					<Table>
						<TableHeader>
							<TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
								<TableHead className="font-medium">Name</TableHead>
								<TableHead className="font-medium">Hauptstandort</TableHead>
								<TableHead className="font-medium">
									Hauptansprechpartner
								</TableHead>
								<TableHead className="w-24 text-center">Aktionen</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{filteredKunden.length === 0 ? (
								<TableRow>
									<TableCell colSpan={4} className="h-24 text-center">
										{searchTerm
											? `Keine Kunden gefunden für "${searchTerm}"`
											: "Keine Kunden vorhanden"}
									</TableCell>
								</TableRow>
							) : (
								filteredKunden.map((kunde) => (
									<TableRow
										key={kunde._id}
										className="border-b border-gray-800"
									>
										<TableCell className="py-3">
											<div className="flex items-center gap-2">
												<div className="w-7 h-7 rounded-full bg-gray-700 flex items-center justify-center text-gray-300">
													<Briefcase className="h-3.5 w-3.5" />
												</div>
												<span className="font-medium">{kunde.name}</span>
											</div>
										</TableCell>
										<TableCell>{getHauptstandortOrt(kunde)}</TableCell>
										<TableCell>{getHauptansprechpartnerName(kunde)}</TableCell>
										<TableCell className="text-center">
											<div className="flex justify-center">
												<Link to={`/kunden/doku/${kunde._id}`}>
													<Button
														variant="ghost"
														size="icon"
														className="h-8 w-8 text-gray-400 hover:text-blue-400"
														title="Dokumentation"
													>
														<BookOpen className="h-4 w-4" />
														<span className="sr-only">Dokumentation</span>
													</Button>
												</Link>
											</div>
										</TableCell>
									</TableRow>
								))
							)}
						</TableBody>
					</Table>
				</div>
			</StandardDataTable>
		</PageLayout>
	);
}

export default KundenDokuPage;
