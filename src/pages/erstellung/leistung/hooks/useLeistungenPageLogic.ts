import { api } from "@/../convex/_generated/api";
import type { Doc, Id } from "@/../convex/_generated/dataModel";
import type { LeistungMitNamen } from "@/components/erstellung/leistung/LeistungDataTable";
import { calculateDateRange, toISODateString } from "@/lib/utils/dateUtils";
import { formatTime, toInputDate, toInputTime } from "@/lib/utils/formatUtils";
import { useMutation, useQuery } from "convex/react";
import { useEffect, useMemo, useState } from "react";
import { toast } from "sonner";

export interface LeistungFilter {
	kundeId: string;
	zeitraum: "all" | "last7" | "last30" | "month" | "lastMonth" | "custom";
	startDatum: string;
	endDatum: string;
}

export type LeistungFormData = {
	_id?: Id<"kunden_leistungen">;
	kundeId: string;
	mitarbeiterId: string;
	kontingentId: string;
	kontingentId2: string;
	datum: string;
	startUhrzeit: string;
	endUhrzeit: string;
	art: "remote" | "vor-Ort" | "vor-Ort (free)";
	stundenpreisInput: string;
	anfahrtskostenInput: string;
	beschreibung: string;
};

interface UseLeistungenPageLogicProps {
	leistungen: LeistungMitNamen[];
	kunden: Doc<"kunden">[]; // Corrected table name
	mitarbeiter: Doc<"mitarbeiter">[]; // Corrected table name
	// kontingente are not directly used in the hook logic moved, but passed to form.
	// If any part of kontingente logic moves here, add its type.
}

export function useLeistungenPageLogic({
	leistungen,
	kunden,
}: UseLeistungenPageLogicProps) {
	const removeLeistungMutation = useMutation(api.erstellung.leistung.remove);

	const [editingId, setEditingId] = useState<Id<"kunden_leistungen"> | null>(
		null,
	);
	const [showForm, setShowForm] = useState(false);
	const [searchTerm, setSearchTerm] = useState("");
	const [filter, setFilter] = useState<LeistungFilter>({
		kundeId: "",
		zeitraum: "last7",
		startDatum: "",
		endDatum: "",
	});
	const [expandedDescriptions, setExpandedDescriptions] = useState<
		Record<Id<"kunden_leistungen">, boolean>
	>({});
	const [showLieferscheinDialog, setShowLieferscheinDialog] = useState(false);
	const [selectedLeistungForLieferschein, setSelectedLeistungForLieferschein] =
		useState<LeistungMitNamen | null>(null);

	const editingLeistung = useMemo(() => {
		return editingId
			? (leistungen.find((l) => l._id === editingId) ?? null)
			: null;
	}, [editingId, leistungen]);

	const initialFormData = useMemo((): LeistungFormData => {
		if (editingLeistung) {
			return {
				_id: editingLeistung._id,
				kundeId: editingLeistung.kundenId,
				mitarbeiterId: editingLeistung.mitarbeiterId,
				kontingentId: editingLeistung.kontingentId,
				kontingentId2: editingLeistung.kontingentId2 || "",
				datum: toInputDate(editingLeistung.startZeit),
				startUhrzeit: toInputTime(editingLeistung.startZeit),
				endUhrzeit: toInputTime(editingLeistung.endZeit),
				art: editingLeistung.art as LeistungFormData["art"],
				stundenpreisInput: editingLeistung.stundenpreis.toString(),
				anfahrtskostenInput: editingLeistung.anfahrtskosten.toString(),
				beschreibung: editingLeistung.beschreibung,
			};
		}
		const now = new Date();
		const startTime = new Date(now.getTime() - 60 * 60 * 1000);
		const defaultKundeId = "";
		const selectedKunde = kunden.find((k) => k._id === defaultKundeId);
		const defaultPreis = selectedKunde
			? selectedKunde.stundenpreis.toString()
			: "";
		const defaultAnfahrt = selectedKunde
			? selectedKunde.anfahrtskosten.toString()
			: "";

		return {
			kundeId: defaultKundeId,
			mitarbeiterId: "",
			kontingentId: "",
			kontingentId2: "",
			datum: toInputDate(now.getTime()),
			startUhrzeit: toInputTime(startTime.getTime()),
			endUhrzeit: toInputTime(now.getTime()),
			art: "remote",
			stundenpreisInput: defaultPreis,
			anfahrtskostenInput: defaultAnfahrt,
			beschreibung: "",
		};
	}, [editingLeistung, kunden]);

	const handleFormSubmitSuccess = () => {
		setShowForm(false);
		setEditingId(null);
	};

	const handleFormCancel = () => {
		setShowForm(false);
		setEditingId(null);
	};

	const handleEdit = (leistung: LeistungMitNamen) => {
		setEditingId(leistung._id);
		setShowForm(true);
	};

	const handleCreateLieferschein = (leistung: LeistungMitNamen) => {
		setSelectedLeistungForLieferschein(leistung);
		setShowLieferscheinDialog(true);
	};

	const handleDelete = async (id: Id<"kunden_leistungen">) => {
		if (
			window.confirm(
				"Möchten Sie diese Leistung wirklich löschen?\n\nDie verbrauchten Stunden werden automatisch von allen verwendeten Kontingenten zurückgegeben.",
			)
		) {
			try {
				await removeLeistungMutation({ id });
				toast.success("Leistung erfolgreich gelöscht");
				if (id === editingId) {
					handleFormCancel();
				}
			} catch (error: any) {
				toast.error(
					`Fehler beim Löschen: ${error.message || "Unbekannter Fehler"}`,
				);
			}
		}
	};

	const handleFilterChange = (key: keyof LeistungFilter, value: string) => {
		setFilter((prev) => {
			let newFilter = { ...prev, [key]: value };

			if (key === "zeitraum") {
				const validZeitraumValues = [
					"all",
					"last7",
					"last30",
					"month",
					"lastMonth",
					"custom",
				] as const;
				const isZeitraum = (
					v: string,
				): v is (typeof validZeitraumValues)[number] =>
					validZeitraumValues.includes(v as any);

				let validatedZeitraum: LeistungFilter["zeitraum"] = "all";
				if (isZeitraum(value)) {
					validatedZeitraum = value;
				}

				if (validatedZeitraum === "all") {
					newFilter = {
						...newFilter,
						zeitraum: "all",
						startDatum: "",
						endDatum: "",
					};
				} else if (validatedZeitraum === "custom") {
					const today = new Date();
					const startDate = newFilter.startDatum
						? new Date(newFilter.startDatum)
						: new Date(today.getFullYear(), today.getMonth(), 1);
					const endDate = newFilter.endDatum
						? new Date(newFilter.endDatum)
						: new Date();

					startDate.setHours(0, 0, 0, 0);
					endDate.setHours(23, 59, 59, 999);

					newFilter = {
						...newFilter,
						zeitraum: "custom",
						startDatum: toISODateString(startDate),
						endDatum: toISODateString(endDate),
					};
				} else {
					const { startDate, endDate } = calculateDateRange(validatedZeitraum);
					endDate.setHours(23, 59, 59, 999);
					newFilter = {
						...newFilter,
						zeitraum: validatedZeitraum,
						startDatum: toISODateString(startDate),
						endDatum: toISODateString(endDate),
					};
				}
			} else if (key === "startDatum" || key === "endDatum") {
				newFilter.zeitraum = "custom";
			}
			return newFilter;
		});
	};

	const handleSearchTermChange = (value: string) => {
		setSearchTerm(value);
	};

	const resetFilters = () => {
		setSearchTerm("");
		setFilter({
			kundeId: "",
			zeitraum: "last7",
			startDatum: "",
			endDatum: "",
		});
	};

	const toggleDescription = (id: Id<"kunden_leistungen">) => {
		setExpandedDescriptions((prev) => ({
			...prev,
			[id]: !prev[id],
		}));
	};

	// Update the date range filter when component mounts or zeitraum changes
	useEffect(() => {
		if (
			filter.zeitraum !== "all" &&
			filter.zeitraum !== "custom" &&
			!filter.startDatum
		) {
			const { startDate, endDate } = calculateDateRange(filter.zeitraum);
			endDate.setHours(23, 59, 59, 999);
			setFilter((prev) => ({
				...prev,
				startDatum: toISODateString(startDate),
				endDatum: toISODateString(endDate),
			}));
		}
	}, [filter.zeitraum, filter.startDatum]);

	const filteredLeistungen = useMemo(() => {
		// Filter the leistungen
		const filtered = leistungen.filter((l) => {
			const searchMatch =
				l.kundeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
				l.mitarbeiterName.toLowerCase().includes(searchTerm.toLowerCase()) ||
				l.beschreibung.toLowerCase().includes(searchTerm.toLowerCase()) ||
				l.kontingentName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
				l.kontingentName2?.toLowerCase().includes(searchTerm.toLowerCase());

			const kundeMatch = !filter.kundeId || l.kundenId === filter.kundeId;

			let dateMatch = true;
			if (filter.zeitraum !== "all") {
				const leistungTime = l.startZeit;
				const startTime = filter.startDatum
					? new Date(filter.startDatum).getTime()
					: 0;
				const endTime = filter.endDatum
					? new Date(filter.endDatum).getTime() + 86400000 - 1 // End of day
					: new Date(8640000000000000).getTime(); // Far future date
				dateMatch = leistungTime >= startTime && leistungTime <= endTime;
			}
			return searchMatch && kundeMatch && dateMatch;
		});

		// Sort by start time, newest first
		return filtered.sort((a, b) => b.startZeit - a.startZeit);
	}, [leistungen, searchTerm, filter]);

	return {
		editingId,
		showForm,
		setShowForm,
		searchTerm,
		filter,
		expandedDescriptions,
		showLieferscheinDialog,
		setShowLieferscheinDialog,
		selectedLeistungForLieferschein,
		initialFormData,
		handleFormSubmitSuccess,
		handleFormCancel,
		handleEdit,
		handleCreateLieferschein,
		handleDelete,
		handleFilterChange,
		handleSearchTermChange,
		resetFilters,
		toggleDescription,
		filteredLeistungen,
	};
}
