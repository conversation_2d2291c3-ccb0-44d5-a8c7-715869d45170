import {
	<PERSON>,
	<PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
} from "@/components/_shared/Card";
import { Skeleton } from "@/components/_shared/Skeleton";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/_shared/Table";
import { EmptyState } from "@/components/layout/EmptyState";
import { formatCurrency, formatHours } from "@/lib/utils/formatUtils";
import { Briefcase, CalendarDays, Car, Clock, Euro, Info } from "lucide-react";

interface PerformanceData {
	stunden: number;
	umsatz: number;
	anzahlLeistungen: number;
	anzahlAnfahrten: number;
}

interface DashboardPerformanceTableProps {
	title: string;
	icon: React.ReactNode;
	data: PerformanceData | null;
	isLoading: boolean;
}

// Loading skeleton component
const LoadingSkeleton = () => (
	<Card className="border-0 shadow-md bg-gray-800/40 overflow-hidden">
		<CardHeader className="p-3 border-b border-gray-700/50">
			<Skeleton className="h-5 w-32" />
		</CardHeader>
		<CardContent className="p-0">
			<div className="p-3">
				{[...Array(4)].map((_, i) => (
					<div key={i} className="flex justify-between py-1.5">
						<Skeleton className="h-4 w-24" />
						<Skeleton className="h-4 w-16" />
					</div>
				))}
			</div>
		</CardContent>
	</Card>
);

// Empty state wrapper component
const EmptyStateWrapper = () => (
	<Card className="border-0 shadow-md bg-gray-800/40 overflow-hidden">
		<CardHeader className="p-3 border-b border-gray-700/50">
			<CardTitle className="text-sm font-medium flex items-center">
				<Info className="w-4 h-4 mr-1.5" />
				Keine Daten
			</CardTitle>
		</CardHeader>
		<CardContent className="p-3">
			<EmptyState
				title=""
				message="Für den ausgewählten Zeitraum wurden keine Daten gefunden."
			/>
		</CardContent>
	</Card>
);

export function DashboardPerformanceTable({
	title,
	icon,
	data,
	isLoading,
}: DashboardPerformanceTableProps) {
	if (isLoading) {
		return <LoadingSkeleton />;
	}

	if (!data || (data.stunden === 0 && data.anzahlLeistungen === 0)) {
		return <EmptyStateWrapper />;
	}

	return (
		<Card className="border-0 shadow-md bg-gray-800/40 overflow-hidden">
			<CardHeader className="p-3 border-b border-gray-700/50">
				<CardTitle className="text-sm font-medium flex items-center">
					{icon}
					{title}
				</CardTitle>
			</CardHeader>
			<CardContent className="p-0">
				<Table>
					<TableBody>
						<TableRow className="border-b border-gray-700/50">
							<TableCell className="px-3 py-2 text-xs">
								<div className="flex items-center gap-1.5">
									<Clock className="w-3.5 h-3.5 text-blue-400" />
									<span>Stunden</span>
								</div>
							</TableCell>
							<TableCell className="px-3 py-2 text-xs text-right font-medium">
								{formatHours(data.stunden)}
							</TableCell>
						</TableRow>
						<TableRow className="border-b border-gray-700/50">
							<TableCell className="px-3 py-2 text-xs">
								<div className="flex items-center gap-1.5">
									<Euro className="w-3.5 h-3.5 text-green-400" />
									<span>Umsatz</span>
								</div>
							</TableCell>
							<TableCell className="px-3 py-2 text-xs text-right font-medium">
								{formatCurrency(data.umsatz)}
							</TableCell>
						</TableRow>
						<TableRow className="border-b border-gray-700/50">
							<TableCell className="px-3 py-2 text-xs">
								<div className="flex items-center gap-1.5">
									<Briefcase className="w-3.5 h-3.5 text-indigo-400" />
									<span>Leistungen</span>
								</div>
							</TableCell>
							<TableCell className="px-3 py-2 text-xs text-right font-medium">
								{data.anzahlLeistungen}
							</TableCell>
						</TableRow>
						<TableRow>
							<TableCell className="px-3 py-2 text-xs">
								<div className="flex items-center gap-1.5">
									<Car className="w-3.5 h-3.5 text-orange-400" />
									<span>Anfahrten</span>
								</div>
							</TableCell>
							<TableCell className="px-3 py-2 text-xs text-right font-medium">
								{data.anzahlAnfahrten}
							</TableCell>
						</TableRow>
					</TableBody>
				</Table>
			</CardContent>
		</Card>
	);
}
