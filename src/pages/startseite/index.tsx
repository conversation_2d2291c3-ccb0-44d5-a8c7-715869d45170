import { api } from "@/../convex/_generated/api";
import { PageLayout } from "@/components/layout/PageLayout";
import { calculateDateRange, toISODateString } from "@/lib/utils/dateUtils";
// import { formatCurrency, formatHours } from "@/lib/utils/formatUtils"; // Not used directly in this component after refactor
import { useQuery } from "convex/react";
import { CalendarClock, CalendarDays } from "lucide-react"; // Users, Hourglass not used
import { useMemo } from "react";

import { DashboardPerformanceTable } from "./DashboardPerformanceTable";
import { DashboardRecentActivity } from "./DashboardRecentActivity";
import { DashboardTables } from "./DashboardTables";
import {
	type DashboardFilter,
	useDashboardAnalytics,
	// type DashboardDaten, // Not needed here as it's the return type of the hook
	// type KundenStatistik, // Not needed here
	// type MitarbeiterStatistik, // Not needed here
	// type KontingentProKundeStatistik, // Not needed here
} from "./hooks/useDashboardAnalytics";

export function DashboardPage() {
	// Queries
	// Ensure correct typing for data fetched by useQuery, matching what useDashboardAnalytics expects
	const allLeistungen =
		(useQuery(api.erstellung.leistung.list) as any[] | undefined) || [];
	const allKontingente =
		(useQuery(api.verwaltung.kontingente.list) as any[] | undefined) || [];

	const isLoading = allLeistungen === undefined || allKontingente === undefined;

	// Fester Filter für den aktuellen Monat
	const filter: DashboardFilter = useMemo(() => {
		const { startDate, endDate } = calculateDateRange("month");
		return {
			zeitraum: "month",
			startDatum: toISODateString(startDate),
			endDatum: toISODateString(endDate),
			kundeId: "all",
			mitarbeiterId: "all",
		};
	}, []);

	// Calculate dashboard data using the custom hook
	const dashboardDaten = useDashboardAnalytics({
		allLeistungen,
		allKontingente,
		filter,
	});

	return (
		<PageLayout
			title="Dashboard"
			subtitle="Übersicht über alle wichtigen Kennzahlen und Aktivitäten."
		>
			<div className="space-y-4">
				<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
					<div className="md:col-span-2 space-y-4">
						<DashboardPerformanceTable
							title="Tagesleistung"
							icon={<CalendarDays className="w-4 h-4 mr-1.5" />}
							data={dashboardDaten?.tagesLeistung || null}
							isLoading={isLoading}
						/>

						<DashboardPerformanceTable
							title="Monatsleistung"
							icon={<CalendarClock className="w-4 h-4 mr-1.5" />}
							data={dashboardDaten?.monatsLeistung || null}
							isLoading={isLoading}
						/>

						<DashboardTables
							dashboardDaten={dashboardDaten}
							isLoading={isLoading}
						/>
					</div>
					<div>
						<DashboardRecentActivity filter={filter} isLoading={isLoading} />
					</div>
				</div>
			</div>
		</PageLayout>
	);
}

export default DashboardPage;
