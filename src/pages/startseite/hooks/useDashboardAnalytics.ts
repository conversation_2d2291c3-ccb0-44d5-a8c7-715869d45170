import { Doc } from "@/../convex/_generated/dataModel"; // Assuming path based on existing imports
import { useMemo } from "react";

// Type definitions
export interface DashboardFilter {
	zeitraum: "month" | "quarter" | "year" | "custom" | "all";
	startDatum: string;
	endDatum: string;
	kundeId: string;
	mitarbeiterId: string;
}

export interface KundenStatistik {
	kundeId: string;
	kundeName: string;
	stunden: number;
	umsatz: number;
	anzahlLeistungen: number;
}

export interface MitarbeiterStatistik {
	mitarbeiterId: string;
	mitarbeiterName: string;
	stunden: number;
	anzahlLeistungen: number;
}

export interface KontingentProKundeStatistik {
	kundeId: string;
	kundeName: string;
	gesamtStunden: number;
	verbrauchtStunden: number;
	restStunden: number;
	anzahlKontingente: number;
}

export interface DashboardDaten {
	gesamtStundenLeistung: number;
	gesamtUmsatz: number;
	anzahlLeistungenGesamt: number;
	aktiveKundenCount: number;
	aktiveMitarbeiterCount: number;
	anzahlAnfahrten: number;
	kundenStatistiken: KundenStatistik[];
	mitarbeiterStatistiken: MitarbeiterStatistik[];
	kontingentStatistiken: KontingentProKundeStatistik[];
	topLeistungenKunden: KundenStatistik[];
	topLeistungenMitarbeiter: MitarbeiterStatistik[];
	tagesLeistung: {
		stunden: number;
		umsatz: number;
		anzahlLeistungen: number;
		anzahlAnfahrten: number;
	};
	monatsLeistung: {
		stunden: number;
		umsatz: number;
		anzahlLeistungen: number;
		anzahlAnfahrten: number;
	};
}

// Define more specific types for data coming from queries, which may include joined fields
type LeistungWithNames = Doc<"kunden_leistungen"> & {
	kundeName: string;
	mitarbeiterName: string;
};

type KontingentWithKundeName = Doc<"kunden_kontingente"> & {
	kundeName: string;
	// restStunden might be calculated by the query or needs to be calculated here
	// If it's not on the object, we'll calculate it from stunden and verbrauchteStunden
};

interface UseDashboardAnalyticsProps {
	allLeistungen: LeistungWithNames[] | undefined;
	allKontingente: KontingentWithKundeName[] | undefined;
	filter: DashboardFilter;
}

export const useDashboardAnalytics = ({
	allLeistungen,
	allKontingente,
	filter,
}: UseDashboardAnalyticsProps): DashboardDaten | null => {
	return useMemo(() => {
		if (!allLeistungen || !allKontingente) return null;

		const filteredLeistungen = allLeistungen.filter((leistung) => {
			const leistungDatum = new Date(leistung.startZeit);
			const startDate = new Date(filter.startDatum);
			const endDate = new Date(filter.endDatum);

			const isInDateRange =
				leistungDatum >= startDate && leistungDatum <= endDate;

			const matchesKunde =
				filter.kundeId === "all" || leistung.kundenId === filter.kundeId;

			const matchesMitarbeiter =
				filter.mitarbeiterId === "all" ||
				leistung.mitarbeiterId === filter.mitarbeiterId;

			return isInDateRange && matchesKunde && matchesMitarbeiter;
		});

		let gesamtStundenLeistung = 0;
		let gesamtUmsatz = 0;
		let anzahlLeistungenGesamt = filteredLeistungen.length;
		let anzahlAnfahrten = 0;

		const aktiveKundenLeistung = new Map<string, boolean>();
		const aktiveMitarbeiter = new Map<string, boolean>();

		const heute = new Date();
		heute.setHours(0, 0, 0, 0);

		const ersterTagDesMonats = new Date(
			heute.getFullYear(),
			heute.getMonth(),
			1,
		);
		ersterTagDesMonats.setHours(0, 0, 0, 0);

		let tagesLeistung = {
			stunden: 0,
			umsatz: 0,
			anzahlLeistungen: 0,
			anzahlAnfahrten: 0,
		};

		let monatsLeistung = {
			stunden: 0,
			umsatz: 0,
			anzahlLeistungen: 0,
			anzahlAnfahrten: 0,
		};

		const kundenStatsMap = new Map<
			string,
			{
				kundeName: string;
				stunden: number;
				umsatz: number;
				anzahlLeistungen: number;
			}
		>();

		const mitarbeiterStatsMap = new Map<
			string,
			{
				mitarbeiterName: string;
				stunden: number;
				anzahlLeistungen: number;
			}
		>();

		filteredLeistungen.forEach((leistung) => {
			const leistungDatum = new Date(leistung.startZeit);
			leistungDatum.setHours(0, 0, 0, 0);

			gesamtStundenLeistung += leistung.stunden;
			gesamtUmsatz += leistung.stunden * leistung.stundenpreis;

			const hatAnfahrt = leistung.mitAnfahrt;
			if (hatAnfahrt) {
				gesamtUmsatz += leistung.anfahrtskosten;
				anzahlAnfahrten++;
			}

			if (leistungDatum.getTime() === heute.getTime()) {
				tagesLeistung.stunden += leistung.stunden;
				tagesLeistung.umsatz += leistung.stunden * leistung.stundenpreis;
				if (hatAnfahrt) {
					tagesLeistung.umsatz += leistung.anfahrtskosten;
					tagesLeistung.anzahlAnfahrten++;
				}
				tagesLeistung.anzahlLeistungen++;
			}

			if (leistungDatum >= ersterTagDesMonats) {
				monatsLeistung.stunden += leistung.stunden;
				monatsLeistung.umsatz += leistung.stunden * leistung.stundenpreis;
				if (hatAnfahrt) {
					monatsLeistung.umsatz += leistung.anfahrtskosten;
					monatsLeistung.anzahlAnfahrten++;
				}
				monatsLeistung.anzahlLeistungen++;
			}

			aktiveKundenLeistung.set(leistung.kundenId, true);
			aktiveMitarbeiter.set(leistung.mitarbeiterId, true);

			const kundeKey = leistung.kundenId;
			const kundeStats = kundenStatsMap.get(kundeKey) || {
				kundeName: leistung.kundeName,
				stunden: 0,
				umsatz: 0,
				anzahlLeistungen: 0,
			};
			kundeStats.stunden += leistung.stunden;
			kundeStats.umsatz += leistung.stunden * leistung.stundenpreis;
			if (leistung.mitAnfahrt) {
				kundeStats.umsatz += leistung.anfahrtskosten;
			}
			kundeStats.anzahlLeistungen++;
			kundenStatsMap.set(kundeKey, kundeStats);

			const mitarbeiterKey = leistung.mitarbeiterId;
			const mitarbeiterStats = mitarbeiterStatsMap.get(mitarbeiterKey) || {
				mitarbeiterName: leistung.mitarbeiterName,
				stunden: 0,
				anzahlLeistungen: 0,
			};
			mitarbeiterStats.stunden += leistung.stunden;
			mitarbeiterStats.anzahlLeistungen++;
			mitarbeiterStatsMap.set(mitarbeiterKey, mitarbeiterStats);
		});

		const kontingentStatsMap = new Map<
			string,
			{
				kundeName: string;
				gesamtStunden: number;
				verbrauchtStunden: number;
				restStunden: number;
				anzahlKontingente: number;
			}
		>();

		allKontingente.forEach((kontingent) => {
			const kundeKey = kontingent.kundenId;
			const stats = kontingentStatsMap.get(kundeKey) || {
				kundeName: kontingent.kundeName,
				gesamtStunden: 0,
				verbrauchtStunden: 0,
				restStunden: 0,
				anzahlKontingente: 0,
			};

			stats.gesamtStunden += kontingent.stunden;
			stats.verbrauchtStunden += kontingent.verbrauchteStunden;
			// Calculate restStunden as it's not directly on the schema
			stats.restStunden += kontingent.stunden - kontingent.verbrauchteStunden;
			stats.anzahlKontingente++;

			kontingentStatsMap.set(kundeKey, stats);
		});

		const kundenStatistiken: KundenStatistik[] = Array.from(
			kundenStatsMap.entries(),
		).map(([kundeId, stats]) => ({ kundeId, ...stats }));

		const mitarbeiterStatistiken: MitarbeiterStatistik[] = Array.from(
			mitarbeiterStatsMap.entries(),
		).map(([mitarbeiterId, stats]) => ({ mitarbeiterId, ...stats }));

		const kontingentStatistiken: KontingentProKundeStatistik[] = Array.from(
			kontingentStatsMap.entries(),
		).map(([kundeId, stats]) => ({ kundeId, ...stats }));

		const topLeistungenKunden = [...kundenStatistiken]
			.sort((a, b) => b.stunden - a.stunden)
			.slice(0, 5);

		const topLeistungenMitarbeiter = [...mitarbeiterStatistiken]
			.sort((a, b) => b.stunden - a.stunden)
			.slice(0, 5);

		return {
			gesamtStundenLeistung,
			gesamtUmsatz,
			anzahlLeistungenGesamt,
			aktiveKundenCount: aktiveKundenLeistung.size,
			aktiveMitarbeiterCount: aktiveMitarbeiter.size,
			anzahlAnfahrten,
			kundenStatistiken,
			mitarbeiterStatistiken,
			kontingentStatistiken,
			topLeistungenKunden,
			topLeistungenMitarbeiter,
			tagesLeistung,
			monatsLeistung,
		};
	}, [allLeistungen, allKontingente, filter]);
};
