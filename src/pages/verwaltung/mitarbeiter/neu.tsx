import { api } from "@/../convex/_generated/api";
import { Button } from "@/components/_shared/Button";
import { PageLayout } from "@/components/layout/PageLayout";
import { MitarbeiterPageForm } from "@/components/verwaltung/mitarbeiter/MitarbeiterPageForm";
import { useMutation } from "convex/react";
import { ArrowLeft, Save } from "lucide-react";
import { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { toast } from "sonner";

export function MitarbeiterNeuPage() {
	const navigate = useNavigate();
	const [isSubmitting, setIsSubmitting] = useState(false);

	// Mutation
	const createMitarbeiter = useMutation(api.verwaltung.mitarbeiter.create);

	const handleSave = async (data: any) => {
		setIsSubmitting(true);
		try {
			const mitarbeiterId = await createMitarbeiter(data);
			toast.success("Mitarbeiter erfolgreich erstellt");
			navigate(`/verwaltung/mitarbeiter/${mitarbeiterId}`);
		} catch (error: any) {
			toast.error(`<PERSON><PERSON> beim <PERSON>: ${error.message}`);
		} finally {
			setIsSubmitting(false);
		}
	};

	const headerActions = (
		<div className="flex items-center gap-2">
			<Link to="/verwaltung/mitarbeiter">
				<Button variant="outline" className="gap-2">
					<ArrowLeft className="h-4 w-4" />
					Zurück
				</Button>
			</Link>

			<Button
				form="mitarbeiter-form"
				type="submit"
				disabled={isSubmitting}
				className="gap-2"
			>
				<Save className="h-4 w-4" />
				{isSubmitting ? "Erstellt..." : "Erstellen"}
			</Button>
		</div>
	);

	return (
		<PageLayout
			title="Neuer Mitarbeiter"
			subtitle="Mitarbeiterdaten erfassen"
			action={headerActions}
		>
			<div className="max-w-4xl mx-auto">
				{/* Form */}
				<MitarbeiterPageForm
					onSubmit={handleSave}
					isSubmitting={isSubmitting}
					formId="mitarbeiter-form"
				/>
			</div>
		</PageLayout>
	);
}

export default MitarbeiterNeuPage;
