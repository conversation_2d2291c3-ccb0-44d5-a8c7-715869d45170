import { api } from "@/../convex/_generated/api";
import { Button } from "@/components/_shared/Button";
import { PageLayout } from "@/components/layout/PageLayout";
import { KundenPageForm } from "@/components/verwaltung/kunden/KundenPageForm";
import { useMutation } from "convex/react";
import { ArrowLeft, Save } from "lucide-react";
import { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { toast } from "sonner";

export function KundenStammdatenNeuPage() {
	const navigate = useNavigate();
	const [isSubmitting, setIsSubmitting] = useState(false);

	// Mutation
	const createKunde = useMutation(api.verwaltung.kunden.create);

	const handleSave = async (data: any) => {
		setIsSubmitting(true);
		try {
			const kundeId = await createKunde(data);
			toast.success("Kunde erfolgreich erstellt");
			navigate(`/verwaltung/kunden/${kundeId}`);
		} catch (error: any) {
			toast.error(`<PERSON><PERSON> beim <PERSON>: ${error.message}`);
		} finally {
			setIsSubmitting(false);
		}
	};

	const headerActions = (
		<div className="flex items-center gap-2">
			<Link to="/verwaltung/kunden">
				<Button variant="outline" className="gap-2">
					<ArrowLeft className="h-4 w-4" />
					Zurück
				</Button>
			</Link>

			<Button
				form="kunden-form"
				type="submit"
				disabled={isSubmitting}
				className="gap-2"
			>
				<Save className="h-4 w-4" />
				{isSubmitting ? "Erstellt..." : "Erstellen"}
			</Button>
		</div>
	);

	return (
		<PageLayout
			title="Neuer Kunde"
			subtitle="Stammdaten erfassen"
			action={headerActions}
		>
			<div className="max-w-6xl mx-auto">
				{/* Form */}
				<KundenPageForm
					onSubmit={handleSave}
					isSubmitting={isSubmitting}
					formId="kunden-form"
				/>
			</div>
		</PageLayout>
	);
}

export default KundenStammdatenNeuPage;
