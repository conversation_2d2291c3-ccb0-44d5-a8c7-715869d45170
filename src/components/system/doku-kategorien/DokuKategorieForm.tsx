import { api } from "@/../convex/_generated/api";
import type { Id } from "@/../convex/_generated/dataModel";
import { FIELD_TYPES } from "@/../convex/schema";
import { Button } from "@/components/_shared/Button";
import {
	<PERSON>,
	CardContent,
	CardHeader,
	CardTitle,
} from "@/components/_shared/Card";
import { Checkbox } from "@/components/_shared/Checkbox";
import { Input } from "@/components/_shared/Input";
import { Label } from "@/components/_shared/Label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/_shared/Select";
import { Textarea } from "@/components/_shared/Textarea";
import { useMutation } from "convex/react";
import { Check, Eye, EyeOff, PlusCircle, Trash2, X } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";

interface FormFeldData {
	feldId: number;
	name: string;
	typ: string;
	istErforderlich: boolean;
	optionen: string;
	placeholder: string;
}

interface KategorieData {
	_id: Id<"system_doku_kategorien">;
	name: string;
	beschreibung: string;
	istVordefiniert: boolean;
	felder: {
		feldId: number;
		name: string;
		typ: string;
		istErforderlich: boolean;
		optionen?: string[];
		placeholder?: string;
	}[];
	reihenfolge: number;
}

interface DokuKategorieFormProps {
	kategorien: KategorieData[];
	editingKategorie: KategorieData | null;
	onSubmitSuccess: () => void;
	onCancel: () => void;
}

export function DokuKategorieForm({
	kategorien,
	editingKategorie,
	onSubmitSuccess,
	onCancel,
}: DokuKategorieFormProps) {
	// const createDokuKategorie = useMutation(api.system.dokuKategorien.create);
	// const updateDokuKategorie = useMutation(api.system.dokuKategorien.update);

	const [formState, setFormState] = useState<{
		name: string;
		beschreibung: string;
		reihenfolge: string;
		felder: FormFeldData[];
	}>({
		name: "",
		beschreibung: "",
		reihenfolge: "10",
		felder: [],
	});

	const [passwordVisibility, setPasswordVisibility] = useState<
		Record<string, boolean>
	>({});

	useEffect(() => {
		if (editingKategorie) {
			setFormState({
				name: editingKategorie.name,
				beschreibung: editingKategorie.beschreibung,
				reihenfolge: editingKategorie.reihenfolge.toString(),
				felder: editingKategorie.felder.map((feld) => ({
					feldId: feld.feldId,
					name: feld.name,
					typ: feld.typ,
					istErforderlich: feld.istErforderlich,
					optionen: feld.optionen ? feld.optionen.join(", ") : "",
					placeholder: feld.placeholder || "",
				})),
			});
		} else {
			setFormState({
				name: "",
				beschreibung: "",
				reihenfolge: "10",
				felder: [
					{
						feldId: 0,
						name: "",
						typ: FIELD_TYPES.TEXT,
						istErforderlich: true,
						optionen: "",
						placeholder: "",
					},
				],
			});
		}
	}, [editingKategorie]);

	const handleInputChange = (
		e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
	) => {
		const { id, value } = e.target;
		setFormState((prev) => ({ ...prev, [id]: value }));
	};

	const handleFieldInputChange = (
		index: number,
		fieldKey: keyof Omit<FormFeldData, "feldId">,
		value: string | boolean,
	) => {
		setFormState((prev) => {
			const newFelder = [...prev.felder];
			(newFelder[index] as any)[fieldKey] = value;
			return { ...prev, felder: newFelder };
		});
	};

	const addField = () => {
		setFormState((prev) => ({
			...prev,
			felder: [
				...prev.felder,
				{
					feldId:
						prev.felder.length > 0
							? Math.max(...prev.felder.map((f) => f.feldId)) + 1
							: 0,
					name: "",
					typ: FIELD_TYPES.TEXT,
					istErforderlich: true,
					optionen: "",
					placeholder: "",
				},
			],
		}));
	};

	const removeField = (index: number) => {
		setFormState((prev) => {
			const newFelder = prev.felder.filter((_, i) => i !== index);
			return { ...prev, felder: newFelder };
		});
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		// Basic validation (can be expanded)
		if (!formState.name || formState.felder.some((f) => !f.name || !f.typ)) {
			toast.error(
				"Bitte Name der Kategorie und Name/Typ für alle Felder angeben.",
			);
			return;
		}

		try {
			/* if (isEditing && editingKategorie) {
				await updateDokuKategorie({ id: editingKategorie._id, ...formData });
				toast.success("Kategorie erfolgreich aktualisiert.");
			} else {
				await createDokuKategorie(formData);
				toast.success("Kategorie erfolgreich erstellt.");
			} */
			toast.info(
				"Speichern von Kategorien erfolgt über die Konfigurationsdatei und Synchronisation.",
			);
			onSubmitSuccess();
		} catch (error) {
			console.error("Fehler beim Speichern der Kategorie:", error);
			toast.error("Fehler beim Speichern der Kategorie.");
		}
	};

	const isPredefinedEditing = editingKategorie?.istVordefiniert ?? false;

	const togglePasswordVisibility = (feldId: number) => {
		setPasswordVisibility((prev) => ({
			...prev,
			[feldId.toString()]: !prev[feldId.toString()],
		}));
	};

	return (
		<Card className="shadow-lg border-0 mb-6">
			<CardHeader className="pb-3 px-5 border-b border-gray-700/50">
				<CardTitle className="text-lg font-medium">
					{editingKategorie
						? "Kategorie bearbeiten"
						: "Neue Kategorie erstellen"}
				</CardTitle>
			</CardHeader>
			<CardContent className="p-5">
				<form onSubmit={handleSubmit} className="space-y-4">
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div className="space-y-2">
							<Label htmlFor="name" className="text-sm">
								Name *
							</Label>
							<Input
								id="name"
								value={formState.name}
								onChange={handleInputChange}
								required
								placeholder="Kategoriename"
								className="bg-gray-800/60 border-gray-700"
								disabled={isPredefinedEditing}
							/>
						</div>
						<div className="space-y-2">
							<Label htmlFor="reihenfolge" className="text-sm">
								Reihenfolge *
							</Label>
							<Input
								id="reihenfolge"
								type="number"
								value={formState.reihenfolge}
								onChange={handleInputChange}
								required
								placeholder="Reihenfolge"
								className="bg-gray-800/60 border-gray-700"
								min="1"
								step="10"
								disabled={isPredefinedEditing}
							/>
						</div>
					</div>

					<div className="space-y-2">
						<Label htmlFor="beschreibung" className="text-sm">
							Beschreibung *
						</Label>
						<Textarea
							id="beschreibung"
							value={formState.beschreibung}
							onChange={handleInputChange}
							required
							placeholder="Beschreibung der Kategorie"
							className="min-h-[100px] bg-gray-800/60 border-gray-700"
							disabled={isPredefinedEditing}
						/>
					</div>

					<div className="space-y-2 pt-4 border-t border-gray-700/30">
						<div className="flex justify-between items-center">
							<h3 className="text-sm font-medium text-gray-300">Felder</h3>
							<Button
								type="button"
								variant="outline"
								size="sm"
								onClick={addField}
								className="gap-1"
								disabled={isPredefinedEditing}
							>
								<PlusCircle className="h-3.5 w-3.5" /> Feld hinzufügen
							</Button>
						</div>

						{formState.felder.map((feld, index) => (
							<div
								key={feld.feldId}
								className="p-3 border border-gray-700/50 rounded-md space-y-3 bg-gray-800/30"
							>
								<div className="grid grid-cols-1 md:grid-cols-4 gap-3">
									<div className="space-y-1">
										<Label htmlFor={`feld-name-${index}`} className="text-xs">
											Feldname *
										</Label>
										<Input
											id={`feld-name-${index}`}
											value={feld.name}
											onChange={(e) =>
												handleFieldInputChange(index, "name", e.target.value)
											}
											required
											placeholder="Feldname"
											className="h-9 bg-gray-800/60 border-gray-700"
											disabled={isPredefinedEditing}
										/>
									</div>
									<div className="space-y-1">
										<Label htmlFor={`feld-typ-${index}`} className="text-xs">
											Feldtyp *
										</Label>
										<Select
											value={feld.typ}
											onValueChange={(value) =>
												handleFieldInputChange(index, "typ", value)
											}
											disabled={isPredefinedEditing}
										>
											<SelectTrigger
												id={`feld-typ-${index}`}
												className="h-9 bg-gray-800/60 border-gray-700"
											>
												<SelectValue placeholder="Feldtyp auswählen" />
											</SelectTrigger>
											<SelectContent>
												<SelectItem value={FIELD_TYPES.TEXT}>Text</SelectItem>
												<SelectItem value={FIELD_TYPES.TEXTAREA}>
													Textbereich
												</SelectItem>
												<SelectItem value={FIELD_TYPES.NUMBER}>Zahl</SelectItem>
												<SelectItem value={FIELD_TYPES.PASSWORD}>
													Passwort
												</SelectItem>
												<SelectItem value={FIELD_TYPES.URL}>URL</SelectItem>
												<SelectItem value={FIELD_TYPES.EMAIL}>
													E-Mail
												</SelectItem>
												<SelectItem value={FIELD_TYPES.DATE}>Datum</SelectItem>
												<SelectItem value={FIELD_TYPES.CHECKBOX}>
													Checkbox
												</SelectItem>
												<SelectItem value={FIELD_TYPES.SELECT}>
													Auswahl (Select)
												</SelectItem>
												<SelectItem value={FIELD_TYPES.PHONE}>
													Telefon
												</SelectItem>
											</SelectContent>
										</Select>
									</div>
									<div className="space-y-1">
										<Label
											htmlFor={`feld-placeholder-${index}`}
											className="text-xs"
										>
											Platzhaltertext
										</Label>
										<Input
											id={`feld-placeholder-${index}`}
											value={feld.placeholder}
											onChange={(e) =>
												handleFieldInputChange(
													index,
													"placeholder",
													e.target.value,
												)
											}
											placeholder="Optionaler Platzhalter"
											className="h-9 bg-gray-800/60 border-gray-700"
											disabled={isPredefinedEditing}
										/>
									</div>
									<div className="flex items-end gap-3">
										<div className="flex items-center h-9 space-x-2">
											<Checkbox
												id={`feld-required-${index}`}
												checked={feld.istErforderlich}
												onCheckedChange={(checked) =>
													handleFieldInputChange(
														index,
														"istErforderlich",
														!!checked,
													)
												}
												disabled={isPredefinedEditing}
											/>
											<Label
												htmlFor={`feld-required-${index}`}
												className="text-xs"
											>
												Erforderlich
											</Label>
										</div>
										{!isPredefinedEditing && formState.felder.length > 1 && (
											<Button
												type="button"
												variant="ghost"
												size="icon"
												onClick={() => removeField(index)}
												className="h-9 w-9 text-red-400 hover:text-red-500 hover:bg-red-500/10"
											>
												<Trash2 className="h-4 w-4" />
											</Button>
										)}
									</div>
								</div>

								{feld.typ === FIELD_TYPES.PASSWORD && (
									<div className="relative">
										<Label
											htmlFor={`feld-value-pw-${index}`}
											className="text-xs"
										>
											Passwortwert
										</Label>
										<Input
											id={`feld-value-pw-${index}`}
											type={
												passwordVisibility[feld.feldId.toString()]
													? "text"
													: "password"
											}
											value={feld.optionen}
											onChange={(e) =>
												handleFieldInputChange(
													index,
													"optionen",
													e.target.value,
												)
											}
											required={feld.istErforderlich}
											placeholder="Passwort eingeben..."
											className="pr-10 bg-gray-800/60 border-gray-700"
											disabled={isPredefinedEditing}
										/>
										<Button
											type="button"
											variant="ghost"
											size="icon"
											className="absolute right-1 top-1 h-8 w-8 text-gray-400"
											onClick={() => togglePasswordVisibility(feld.feldId)}
											disabled={isPredefinedEditing}
										>
											{passwordVisibility[feld.feldId.toString()] ? (
												<EyeOff className="h-4 w-4" />
											) : (
												<Eye className="h-4 w-4" />
											)}
										</Button>
									</div>
								)}

								{feld.typ === FIELD_TYPES.SELECT && (
									<div className="space-y-1">
										<Label
											htmlFor={`feld-options-${index}`}
											className="text-xs"
										>
											Optionen * (durch Komma getrennt)
										</Label>
										<Input
											id={`feld-options-${index}`}
											value={feld.optionen}
											onChange={(e) =>
												handleFieldInputChange(
													index,
													"optionen",
													e.target.value,
												)
											}
											required
											placeholder="Option 1, Option 2, Option 3"
											className="h-9 bg-gray-800/60 border-gray-700"
											disabled={isPredefinedEditing}
										/>
									</div>
								)}
							</div>
						))}
					</div>

					<div className="flex gap-2 pt-4 border-t border-gray-700/30">
						<Button
							type="submit"
							size="sm"
							className="gap-1"
							disabled={isPredefinedEditing}
						>
							<Check className="h-4 w-4" />
							{editingKategorie ? "Aktualisieren" : "Speichern"}
						</Button>
						<Button
							type="button"
							variant="outline"
							size="sm"
							onClick={onCancel}
							className="gap-1"
						>
							<X className="h-4 w-4" /> Abbrechen
						</Button>
					</div>
				</form>
			</CardContent>
		</Card>
	);
}
