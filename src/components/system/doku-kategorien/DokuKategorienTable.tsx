import type { Id } from "@/../convex/_generated/dataModel";
import { FIELD_TYPES } from "@/../convex/schema";
import { Badge } from "@/components/_shared/Badge";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/_shared/Table";
import { cn } from "@/lib/utils/cn";
import {
	<PERSON><PERSON><PERSON><PERSON>gle,
	Copy,
	Eye,
	EyeOff,
	ListChecks,
	MessageSquareText,
	Sparkles,
} from "lucide-react";

const fieldTypeColors: Record<string, string> = {
	[FIELD_TYPES.TEXT]: "bg-sky-600/70 text-sky-100 border-sky-500",
	[FIELD_TYPES.TEXTAREA]: "bg-teal-600/70 text-teal-100 border-teal-500",
	[FIELD_TYPES.NUMBER]: "bg-amber-600/70 text-amber-100 border-amber-500",
	[FIELD_TYPES.PASSWORD]: "bg-rose-600/70 text-rose-100 border-rose-500",
	[FIELD_TYPES.URL]: "bg-indigo-600/70 text-indigo-100 border-indigo-500",
	[FIELD_TYPES.EMAIL]: "bg-purple-600/70 text-purple-100 border-purple-500",
	[FIELD_TYPES.DATE]: "bg-lime-600/70 text-lime-100 border-lime-500",
	[FIELD_TYPES.CHECKBOX]: "bg-pink-600/70 text-pink-100 border-pink-500",
	[FIELD_TYPES.SELECT]: "bg-cyan-600/70 text-cyan-100 border-cyan-500",
	[FIELD_TYPES.PHONE]: "bg-orange-600/70 text-orange-100 border-orange-500",
	default: "bg-gray-600/70 text-gray-100 border-gray-500",
};

interface FeldPropBadgeProps {
	condition?: boolean;
	IconComponent: React.ElementType;
	tooltip: string;
	className: string;
	char?: string;
}

const FeldPropBadge: React.FC<FeldPropBadgeProps> = ({
	condition,
	IconComponent,
	tooltip,
	className,
	char,
}) => {
	if (!condition) return null;
	return (
		<Badge
			variant="outline"
			className={cn("px-1.5 py-0.5 text-xs font-normal border", className)}
			title={tooltip}
		>
			<IconComponent className="h-3 w-3 mr-0.5" /> {char}
		</Badge>
	);
};

interface DokuKategorienTableProps {
	kategorien: {
		_id: Id<"system_doku_kategorien">;
		name: string;
		beschreibung: string;
		felder: {
			feldId: number;
			name: string;
			typ: string;
			istErforderlich: boolean;
			optionen?: string[];
			placeholder?: string;
			istKopierbar?: boolean;
			istVersteckt?: boolean;
			istExtrafeld?: boolean;
		}[];
		kategorieID?: number;
		reihenfolge: number;
	}[];
}

export function DokuKategorienTable({ kategorien }: DokuKategorienTableProps) {
	return (
		<div className="overflow-x-auto rounded-lg border border-gray-700/80 shadow-sm">
			<Table>
				<TableHeader>
					<TableRow className="bg-gray-800/50 hover:bg-gray-800/50 border-b-gray-700/80">
						<TableHead className="font-semibold text-gray-200 w-[35%]">
							Kategorie Details
						</TableHead>
						<TableHead className="font-semibold text-gray-200 w-[65%]">
							Felder
						</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{kategorien.length === 0 ? (
						<TableRow className="border-gray-700/80">
							<TableCell colSpan={2} className="h-24 text-center text-gray-400">
								Keine Kategorien vorhanden
							</TableCell>
						</TableRow>
					) : (
						kategorien.map((kategorie) => (
							<TableRow
								key={kategorie._id}
								className="border-gray-700/80 hover:bg-gray-800/30"
							>
								<TableCell className="py-3 align-top">
									<div className="flex flex-col">
										<span className="text-base font-semibold text-gray-100 mb-1">
											{kategorie.name}
										</span>
										<p className="text-xs text-gray-400 mb-1.5 leading-relaxed">
											{kategorie.beschreibung}
										</p>
										<span className="block text-xs text-gray-500">
											Reihenfolge: {kategorie.reihenfolge}
										</span>
										{kategorie.kategorieID !== undefined && (
											<span className="block text-xs text-gray-500">
												ID: {kategorie.kategorieID}
											</span>
										)}
									</div>
								</TableCell>
								<TableCell className="py-3 align-top">
									{kategorie.felder.length === 0 ? (
										<span className="text-xs text-gray-500">
											Keine Felder definiert.
										</span>
									) : (
										<div className="space-y-1.5">
											{kategorie.felder.map((feld) => (
												<div
													key={feld.feldId || feld.name}
													className="flex items-center flex-wrap gap-1.5 group"
												>
													<span
														className={cn(
															"text-sm font-medium text-gray-200 whitespace-nowrap px-1.5 py-0.5 rounded-md border",
															fieldTypeColors[
																feld.typ as keyof typeof FIELD_TYPES
															] || fieldTypeColors.default,
														)}
													>
														{feld.name}
													</span>
													<FeldPropBadge
														condition={feld.istErforderlich}
														IconComponent={AlertTriangle}
														tooltip="Erforderlich"
														className="bg-red-500/20 text-red-300 border-red-500/40"
														char="R"
													/>
													<FeldPropBadge
														condition={feld.istKopierbar}
														IconComponent={Copy}
														tooltip="Kopierbar"
														className="bg-blue-500/20 text-blue-300 border-blue-500/40"
														char="K"
													/>
													<FeldPropBadge
														condition={feld.istVersteckt}
														IconComponent={EyeOff}
														tooltip="Versteckt"
														className="bg-purple-500/20 text-purple-300 border-purple-500/40"
														char="V"
													/>
													<FeldPropBadge
														condition={feld.istExtrafeld}
														IconComponent={Sparkles}
														tooltip="Extrafeld"
														className="bg-green-500/20 text-green-300 border-green-500/40"
														char="E"
													/>
													<FeldPropBadge
														condition={
															!!feld.optionen && feld.optionen.length > 0
														}
														IconComponent={ListChecks}
														tooltip={`Optionen: ${feld.optionen?.join(", ")}`}
														className="bg-indigo-500/20 text-indigo-300 border-indigo-500/40"
														char="O"
													/>
													<FeldPropBadge
														condition={!!feld.placeholder}
														IconComponent={MessageSquareText}
														tooltip={`Placeholder: ${feld.placeholder}`}
														className="bg-yellow-500/20 text-yellow-300 border-yellow-500/40"
														char="P"
													/>
												</div>
											))}
										</div>
									)}
								</TableCell>
							</TableRow>
						))
					)}
				</TableBody>
			</Table>
		</div>
	);
}
