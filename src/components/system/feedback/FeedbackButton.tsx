import { AlertCircle } from "lucide-react";
import React, { useState } from "react";
import { FeedbackPortal } from "./FeedbackPortal";

interface FeedbackButtonProps {
	className?: string;
}

export function FeedbackButton({ className }: FeedbackButtonProps) {
	const [isDialogOpen, setIsDialogOpen] = useState(false);

	return (
		<>
			<button
				onClick={() => setIsDialogOpen(true)}
				className={className}
				title="Feedback geben oder Fehler melden"
			>
				<AlertCircle className="h-4 w-4" />
			</button>

			<FeedbackPortal
				isOpen={isDialogOpen}
				onClose={() => setIsDialogOpen(false)}
			/>
		</>
	);
}
