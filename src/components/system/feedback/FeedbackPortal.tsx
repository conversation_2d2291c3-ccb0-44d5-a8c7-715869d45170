import React, { useEffect, useState } from "react";
import { createPortal } from "react-dom";
import { FeedbackDialog } from "./FeedbackDialog";

interface FeedbackPortalProps {
	isOpen: boolean;
	onClose: () => void;
}

export function FeedbackPortal({ isOpen, onClose }: FeedbackPortalProps) {
	const [mounted, setMounted] = useState(false);

	useEffect(() => {
		setMounted(true);
		return () => setMounted(false);
	}, []);

	// Wenn nicht gemounted oder nicht geöffnet, nichts rendern
	if (!mounted || !isOpen) return null;

	// Portal zum body-Element erstellen
	return createPortal(
		<FeedbackDialog isOpen={isOpen} onClose={onClose} />,
		document.body,
	);
}
