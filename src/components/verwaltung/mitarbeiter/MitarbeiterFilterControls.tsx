import {
	type FilterConfig,
	GenericFilterControls,
} from "@/components/layout/GenericFilterControls";
import React from "react";

interface MitarbeiterFilterControlsProps {
	searchTerm: string;
	onSearchTermChange: (value: string) => void;
	itemCount: number;
	onResetAllFilters?: () => void;
}

export function MitarbeiterFilterControls({
	searchTerm,
	onSearchTermChange,
	itemCount,
	onResetAllFilters,
}: MitarbeiterFilterControlsProps) {
	const filtersConfig: FilterConfig[] = [
		{
			type: "search",
			id: "mitarbeiterSearch",
			value: searchTerm,
			onChange: onSearchTermChange,
			placeholder: "Mitarbeiter suchen...",
			// Original class for search div: "relative flex-grow w-full sm:w-auto max-w-xs"
			className: "relative flex-grow w-full sm:w-auto max-w-xs",
			inputClassName: "h-8 text-xs pl-8 w-full bg-gray-800 border-gray-700",
		},
		{
			type: "custom",
			id: "itemCountDisplay",
			node: (
				<div className="text-xs text-gray-400 whitespace-nowrap">
					{itemCount} Einträge
				</div>
			),
		},
	];

	return (
		<GenericFilterControls
			filters={filtersConfig}
			onResetAll={onResetAllFilters}
			// Original wrapper div: "flex items-center gap-2"
			// This matches the default of GenericFilterControls, so no className override needed here.
		/>
	);
}
