import type { Id } from "@/../convex/_generated/dataModel";
import { Doc } from "@/../convex/_generated/dataModel";
import { Button } from "@/components/_shared/Button";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/_shared/Table";
import { Pencil, Trash2, UserCircle } from "lucide-react";
import { Link } from "react-router-dom";

// Typ übernehmen
interface Mitarbeiter {
	_id: Id<"mitarbeiter">;
	name: string;
	email: string;
}

interface MitarbeiterDataTableProps {
	mitarbeiter: Mitarbeiter[];
	onDelete: (id: Id<"mitarbeiter">) => void;
}

export function MitarbeiterDataTable({
	mitarbeiter,
	onDelete,
}: MitarbeiterDataTableProps) {
	return (
		<div className="overflow-x-auto">
			<Table>
				<TableHeader>
					<TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
						<TableHead className="font-medium">Name</TableHead>
						<TableHead className="font-medium">E-Mail</TableHead>
						<TableHead className="w-24 text-center">Aktionen</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{mitarbeiter.map((m) => (
						<TableRow key={m._id} className="border-b border-gray-800">
							<TableCell className="py-3">
								<div className="flex items-center gap-2">
									<div className="w-7 h-7 rounded-full bg-gray-700 flex items-center justify-center text-gray-300">
										<UserCircle className="h-3.5 w-3.5" />
									</div>
									<span className="font-medium">{m.name}</span>
								</div>
							</TableCell>
							<TableCell>{m.email}</TableCell>
							<TableCell>
								<div className="flex justify-center gap-1">
									<Link to={`/verwaltung/mitarbeiter/${m._id}`}>
										<Button
											variant="ghost"
											size="icon"
											className="h-8 w-8 text-gray-400 hover:text-blue-400"
											title="Bearbeiten"
										>
											<Pencil className="h-4 w-4" />
											<span className="sr-only">Bearbeiten</span>
										</Button>
									</Link>
									<Button
										variant="ghost"
										size="icon"
										onClick={() => void onDelete(m._id)}
										className="h-8 w-8 text-gray-400 hover:text-red-400"
										title="Löschen"
									>
										<Trash2 className="h-4 w-4" />
										<span className="sr-only">Löschen</span>
									</Button>
								</div>
							</TableCell>
						</TableRow>
					))}
				</TableBody>
			</Table>
		</div>
	);
}
