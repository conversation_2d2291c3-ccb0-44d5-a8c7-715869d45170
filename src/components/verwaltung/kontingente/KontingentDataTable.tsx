import type { Doc, Id } from "@/../convex/_generated/dataModel";
import { Badge } from "@/components/_shared/Badge";
import { Button } from "@/components/_shared/Button";
import { Progress } from "@/components/_shared/Progress";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/_shared/Table";
import { cn } from "@/lib/utils/cn";
import {
	Briefcase,
	CalendarDays,
	Hourglass,
	Pencil,
	Trash2,
} from "lucide-react";
import React from "react";

// Übernommenen Typ oder neu definieren
interface KontingentMitKunde {
	_id: Id<"kunden_kontingente">;
	_creationTime: number;
	kundenId: Id<"kunden">;
	name: string;
	stunden: number;
	verbrauchteStunden: number;
	startDatum: number;
	endDatum: number;
	istAktiv: boolean;
	kundeName: string;
	restStunden: number;
}

interface StatusInfo {
	text: string;
	color: string;
	icon: React.ReactNode;
}

interface KontingentDataTableProps {
	kontingente: KontingentMitKunde[];
	onEdit: (kontingent: KontingentMitKunde) => void;
	onDelete: (id: Id<"kunden_kontingente">) => void;
	formatDate: (timestamp: number) => string;
	getStatusInfo: (kontingent: KontingentMitKunde) => StatusInfo;
}

// Hilfsfunktion zur Berechnung der verbleibenden Tage
const calculateRemainingDays = (endTimestamp: number): number => {
	const now = new Date();
	const endDate = new Date(endTimestamp);
	// Zeiten auf Mitternacht setzen für genauen Tagesvergleich
	now.setHours(0, 0, 0, 0);
	endDate.setHours(0, 0, 0, 0);

	const diffTime = endDate.getTime() - now.getTime();
	if (diffTime < 0) {
		return 0; // Bereits abgelaufen
	}
	// Millisekunden in Tage umrechnen und aufrunden
	const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
	return diffDays;
};

export function KontingentDataTable({
	kontingente,
	onEdit,
	onDelete,
	formatDate,
	getStatusInfo,
}: KontingentDataTableProps) {
	return (
		<div className="overflow-x-auto">
			<Table>
				<TableHeader>
					<TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
						<TableHead className="font-medium">Kunde & Kontingent</TableHead>
						<TableHead className="font-medium text-right">Nutzung</TableHead>
						<TableHead className="font-medium">Gültigkeit & Status</TableHead>
						<TableHead className="w-24 text-center">Aktionen</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{kontingente.map((kontingent) => {
						const progress =
							kontingent.stunden > 0
								? (kontingent.verbrauchteStunden / kontingent.stunden) * 100
								: 0;
						const status = getStatusInfo(kontingent);
						return (
							<TableRow
								key={kontingent._id}
								className="border-b border-gray-800"
							>
								<TableCell className="py-3">
									<div className="flex items-center gap-2">
										<div className="w-7 h-7 rounded-full bg-gray-700 flex items-center justify-center text-gray-300">
											<Briefcase className="h-3.5 w-3.5" />
										</div>
										<span className="font-medium">{kontingent.kundeName}</span>
									</div>
									<div className="text-xs text-gray-400 mt-1 ml-9">
										{kontingent.name}
									</div>
								</TableCell>
								<TableCell className="text-right">
									<div className="font-medium mb-1">
										{kontingent.restStunden.toFixed(2)} h{" "}
										<span className="text-xs text-gray-400">Rest</span>
									</div>
									<Progress
										value={progress}
										className="h-1.5 bg-gray-700"
										indicatorClassName={`bg-blue-500 ${progress > 85 ? "bg-red-500" : ""}`}
									/>
									<div className="text-xs text-gray-400 mt-1">
										{kontingent.verbrauchteStunden.toFixed(2)} /{" "}
										{kontingent.stunden.toFixed(1)} h
									</div>
								</TableCell>
								<TableCell>
									<div className="flex items-center gap-1.5 text-sm mb-1">
										<CalendarDays className="h-3.5 w-3.5 text-gray-400" />
										<span>
											{formatDate(kontingent.startDatum)} -{" "}
											{formatDate(kontingent.endDatum)}
											{/* Verbleibende Tage anzeigen, wenn aktiv und nicht abgelaufen */}
											{status.text === "Aktiv" ||
											status.text === "Zukünftig" ? (
												<span className="ml-1.5 inline-flex items-center text-xs text-gray-400">
													<Hourglass className="h-3 w-3 mr-0.5" />{" "}
													{calculateRemainingDays(kontingent.endDatum)} T.
												</span>
											) : null}
										</span>
									</div>
									<Badge
										variant="outline"
										className={`text-xs ${status.color} border-current`}
									>
										{status.icon}
										{status.text}
									</Badge>
								</TableCell>
								<TableCell>
									<div className="flex justify-center gap-1">
										<Button
											variant="ghost"
											size="icon"
											onClick={() => onEdit(kontingent)}
											className="h-8 w-8 text-gray-400 hover:text-blue-400"
											title="Bearbeiten"
										>
											<Pencil className="h-4 w-4" />
											<span className="sr-only">Bearbeiten</span>
										</Button>
										<Button
											variant="ghost"
											size="icon"
											onClick={() => void onDelete(kontingent._id)}
											className="h-8 w-8 text-gray-400 hover:text-red-400"
											title="Löschen"
										>
											<Trash2 className="h-4 w-4" />
											<span className="sr-only">Löschen</span>
										</Button>
									</div>
								</TableCell>
							</TableRow>
						);
					})}
				</TableBody>
			</Table>
		</div>
	);
}
