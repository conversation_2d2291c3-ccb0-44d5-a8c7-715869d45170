import {
	type FilterConfig,
	GenericFilterControls,
} from "@/components/layout/GenericFilterControls";
import React from "react";

interface KundenFilterControlsProps {
	searchTerm: string;
	onSearchTermChange: (value: string) => void;
	onResetAllFilters?: () => void;
}

export function KundenFilterControls({
	searchTerm,
	onSearchTermChange,
	onResetAllFilters,
}: KundenFilterControlsProps) {
	const filtersConfig: FilterConfig[] = [
		{
			type: "search",
			id: "kundenSearch",
			value: searchTerm,
			onChange: onSearchTermChange,
			placeholder: "Kunde suchen...",
			className: "flex-grow",
			inputClassName: "h-8 text-xs pl-8 w-full bg-gray-800 border-gray-700",
		},
	];

	return (
		<GenericFilterControls
			filters={filtersConfig}
			onResetAll={onResetAllFilters}
			// className="flex items-center gap-2" // To match original exactly if needed
		/>
	);
}
