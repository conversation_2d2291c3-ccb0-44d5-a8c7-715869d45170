import { Doc } from "@/../convex/_generated/dataModel";
import { Button } from "@/components/_shared/Button";
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
} from "@/components/_shared/Card";
import { Checkbox } from "@/components/_shared/Checkbox";
import { Input } from "@/components/_shared/Input";
import { Label } from "@/components/_shared/Label";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/_shared/Table";
import { Check, Euro, Mail, Plus, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";

interface StandortData {
	strasse: string;
	plz: string;
	ort: string;
	land?: string;
	istHauptstandort: boolean;
}

interface AnsprechpartnerData {
	name: string;
	email?: string;
	telefon?: string;
	mobil?: string;
	position?: string;
	istHauptansprechpartner: boolean;
	istEmailLieferscheinEmpfaenger?: boolean;
	istEmailUebersichtEmpfaenger?: boolean;
	istEmailAnrede?: boolean;
}

interface KundeFormData {
	name: string;
	stundensatz: string;
	anfahrtskosten: string;
	standorte: StandortData[];
	ansprechpartner: AnsprechpartnerData[];
}

interface KundenPageFormProps {
	initialData?: Doc<"kunden">;
	onSubmit: (data: any) => void;
	isSubmitting: boolean;
	formId: string;
}

const defaultStandort: StandortData = {
	strasse: "",
	plz: "",
	ort: "",
	land: "Deutschland",
	istHauptstandort: false,
};

const defaultAnsprechpartner: AnsprechpartnerData = {
	name: "",
	email: "",
	telefon: "",
	mobil: "",
	position: "",
	istHauptansprechpartner: false,
	istEmailLieferscheinEmpfaenger: false,
	istEmailUebersichtEmpfaenger: false,
	istEmailAnrede: false,
};

export function KundenPageForm({
	initialData,
	onSubmit,
	isSubmitting,
	formId,
}: KundenPageFormProps) {
	const [formState, setFormState] = useState<KundeFormData>({
		name: "",
		stundensatz: "",
		anfahrtskosten: "",
		standorte: [{ ...defaultStandort, istHauptstandort: true }],
		ansprechpartner: [
			{ ...defaultAnsprechpartner, istHauptansprechpartner: true },
		],
	});

	useEffect(() => {
		if (initialData) {
			setFormState({
				name: initialData.name,
				stundensatz: initialData.stundenpreis?.toString() || "",
				anfahrtskosten: initialData.anfahrtskosten?.toString() || "",
				standorte: initialData.standorte?.length
					? initialData.standorte
					: [{ ...defaultStandort, istHauptstandort: true }],
				ansprechpartner: initialData.ansprechpartner?.length
					? initialData.ansprechpartner
					: [{ ...defaultAnsprechpartner, istHauptansprechpartner: true }],
			});
		}
	}, [initialData]);

	const handleInputChange = (
		e: React.ChangeEvent<HTMLInputElement>,
		arrayName?: keyof Pick<KundeFormData, "standorte" | "ansprechpartner">,
		index?: number,
	) => {
		const { name, value, type, checked } = e.target;
		const propValue = type === "checkbox" ? checked : value;

		if (arrayName && index !== undefined) {
			setFormState((prev) => {
				const newArray = [...prev[arrayName]];
				(newArray[index] as any)[name] = propValue;

				// Handle primary toggling
				if (name === "istHauptstandort" && checked) {
					newArray.forEach((item, i) => {
						if (i !== index) (item as StandortData).istHauptstandort = false;
					});
				}
				if (name === "istHauptansprechpartner" && checked) {
					newArray.forEach((item, i) => {
						if (i !== index)
							(item as AnsprechpartnerData).istHauptansprechpartner = false;
					});
				}
				return { ...prev, [arrayName]: newArray };
			});
		} else {
			setFormState((prev) => ({ ...prev, [name]: propValue }));
		}
	};

	const addToArray = (
		arrayName: keyof Pick<KundeFormData, "standorte" | "ansprechpartner">,
	) => {
		setFormState((prev) => ({
			...prev,
			[arrayName]: [
				...prev[arrayName],
				arrayName === "standorte"
					? { ...defaultStandort }
					: { ...defaultAnsprechpartner },
			],
		}));
	};

	const removeFromArray = (
		arrayName: keyof Pick<KundeFormData, "standorte" | "ansprechpartner">,
		index: number,
	) => {
		setFormState((prev) => ({
			...prev,
			[arrayName]: prev[arrayName].filter((_, i) => i !== index),
		}));
	};

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();

		// Validate E-Mail-Anrede (only one allowed)
		const emailAnredeCount = formState.ansprechpartner.filter(
			(a) => a.istEmailAnrede,
		).length;
		if (emailAnredeCount > 1) {
			alert(
				"Nur ein Ansprechpartner kann als E-Mail-Anrede ausgewählt werden.",
			);
			return;
		}

		const { stundensatz, ...restFormState } = formState;
		const processedData = {
			...restFormState,
			stundenpreis: stundensatz ? parseFloat(stundensatz) : 0,
			anfahrtskosten: formState.anfahrtskosten
				? parseFloat(formState.anfahrtskosten)
				: 0,
			ansprechpartner: formState.ansprechpartner.map((a) => ({
				...a,
				email: a.email || undefined,
				telefon: a.telefon || undefined,
				mobil: a.mobil || undefined,
				position: a.position || undefined,
				istEmailAnrede: a.istEmailAnrede || false,
				istEmailLieferscheinEmpfaenger:
					a.istEmailLieferscheinEmpfaenger || false,
				istEmailUebersichtEmpfaenger: a.istEmailUebersichtEmpfaenger || false,
			})),
		};

		onSubmit(processedData);
	};

	return (
		<form id={formId} onSubmit={handleSubmit} className="space-y-4">
			{/* Basic Information */}
			<Card>
				<CardHeader className="pb-3">
					<CardTitle className="text-lg">Grunddaten</CardTitle>
				</CardHeader>
				<CardContent className="space-y-3 pt-2">
					<div className="grid grid-cols-1 md:grid-cols-3 gap-3">
						<div>
							<Label htmlFor="name" className="text-sm">
								Firmenname *
							</Label>
							<Input
								id="name"
								name="name"
								value={formState.name}
								onChange={handleInputChange}
								required
								placeholder="Firmenname"
								className="h-8 text-sm"
							/>
						</div>
						<div>
							<Label htmlFor="stundensatz" className="text-sm">
								Stundensatz €
							</Label>
							<Input
								id="stundensatz"
								name="stundensatz"
								type="number"
								step="0.01"
								value={formState.stundensatz}
								onChange={handleInputChange}
								placeholder="0.00"
								className="h-8 text-sm"
							/>
						</div>
						<div>
							<Label htmlFor="anfahrtskosten" className="text-sm">
								Anfahrtskosten €
							</Label>
							<Input
								id="anfahrtskosten"
								name="anfahrtskosten"
								type="number"
								step="0.01"
								value={formState.anfahrtskosten}
								onChange={handleInputChange}
								placeholder="0.00"
								className="h-8 text-sm"
							/>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Standorte */}
			<Card>
				<CardHeader className="pb-3">
					<div className="flex items-center justify-between">
						<CardTitle className="text-lg">Standorte</CardTitle>
						<Button
							type="button"
							variant="outline"
							size="sm"
							onClick={() => addToArray("standorte")}
							className="h-8 text-sm gap-1"
						>
							<Plus className="h-4 w-4" />
							Standort hinzufügen
						</Button>
					</div>
				</CardHeader>
				<CardContent className="p-0">
					<Table>
						<TableHeader>
							<TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
								<TableHead className="w-12 text-center py-2 px-2 text-xs">
									Haupt
								</TableHead>
								<TableHead className="w-48 py-2 px-3 text-xs">Straße *</TableHead>
								<TableHead className="w-20 py-2 px-3 text-xs">PLZ *</TableHead>
								<TableHead className="w-32 py-2 px-3 text-xs">Ort *</TableHead>
								<TableHead className="w-28 py-2 px-3 text-xs">Land</TableHead>
								<TableHead className="w-16 text-center py-2 px-3 text-xs">
									Aktionen
								</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{formState.standorte.map((standort, index) => (
								<TableRow key={index}>
									<TableCell className="text-center py-2 px-2">
										<Checkbox
											id={`standort-${index}-haupt`}
											name="istHauptstandort"
											checked={standort.istHauptstandort}
											onCheckedChange={(checked) =>
												handleInputChange(
													{
														target: {
															name: "istHauptstandort",
															type: "checkbox",
															checked,
														},
													} as any,
													"standorte",
													index,
												)
											}
										/>
									</TableCell>
									<TableCell className="py-2 px-3">
										<Input
											name="strasse"
											value={standort.strasse}
											onChange={(e) => handleInputChange(e, "standorte", index)}
											required
											placeholder="Straße und Hausnummer"
											className="h-8 text-sm border-gray-600"
										/>
									</TableCell>
									<TableCell className="py-2 px-3">
										<Input
											name="plz"
											value={standort.plz}
											onChange={(e) => handleInputChange(e, "standorte", index)}
											required
											placeholder="12345"
											className="h-8 text-sm border-gray-600"
										/>
									</TableCell>
									<TableCell className="py-2 px-3">
										<Input
											name="ort"
											value={standort.ort}
											onChange={(e) => handleInputChange(e, "standorte", index)}
											required
											placeholder="Stadt"
											className="h-8 text-sm border-gray-600"
										/>
									</TableCell>
									<TableCell className="py-2 px-3">
										<Input
											name="land"
											value={standort.land || ""}
											onChange={(e) => handleInputChange(e, "standorte", index)}
											placeholder="Deutschland"
											className="h-8 text-sm border-gray-600"
										/>
									</TableCell>
									<TableCell className="text-center py-2 px-3">
										{formState.standorte.length > 1 && (
											<Button
												type="button"
												variant="ghost"
												size="icon"
												onClick={() => removeFromArray("standorte", index)}
												className="h-6 w-6 text-red-400 hover:text-red-500 hover:bg-red-500/10"
												title="Standort löschen"
											>
												<Trash2 className="h-3 w-3" />
											</Button>
										)}
									</TableCell>
								</TableRow>
							))}
						</TableBody>
					</Table>
				</CardContent>
			</Card>

			{/* Ansprechpartner */}
			<Card>
				<CardHeader className="pb-3">
					<div className="flex items-center justify-between">
						<CardTitle className="text-lg">Ansprechpartner</CardTitle>
						<Button
							type="button"
							variant="outline"
							size="sm"
							onClick={() => addToArray("ansprechpartner")}
							className="h-8 text-sm gap-1"
						>
							<Plus className="h-4 w-4" />
							Ansprechpartner hinzufügen
						</Button>
					</div>
				</CardHeader>
				<CardContent className="p-0">
					<Table>
						<TableHeader>
							<TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
								<TableHead className="w-12 text-center py-2 px-2 text-xs">
									Haupt
								</TableHead>
								<TableHead className="py-2 px-2 text-xs">Name *</TableHead>
								<TableHead className="py-2 px-2 text-xs">Position</TableHead>
								<TableHead className="py-2 px-2 text-xs">E-Mail</TableHead>
								<TableHead className="py-2 px-2 text-xs">Telefon</TableHead>
								<TableHead className="py-2 px-2 text-xs">Mobil</TableHead>
								<TableHead className="w-32 text-center py-2 px-2 text-xs">
									E-Mail
								</TableHead>
								<TableHead className="w-16 text-center py-2 px-2 text-xs">
									Aktionen
								</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{formState.ansprechpartner.map((partner, index) => (
								<TableRow key={index}>
									<TableCell className="text-center py-2 px-2">
										<Checkbox
											id={`partner-${index}-haupt`}
											name="istHauptansprechpartner"
											checked={partner.istHauptansprechpartner}
											onCheckedChange={(checked) =>
												handleInputChange(
													{
														target: {
															name: "istHauptansprechpartner",
															type: "checkbox",
															checked,
														},
													} as any,
													"ansprechpartner",
													index,
												)
											}
										/>
									</TableCell>
									<TableCell className="py-2 px-2">
										<Input
											name="name"
											value={partner.name}
											onChange={(e) =>
												handleInputChange(e, "ansprechpartner", index)
											}
											required
											placeholder="Vor- und Nachname"
											className="h-8 text-sm border-gray-600"
										/>
									</TableCell>
									<TableCell className="py-2 px-2">
										<Input
											name="position"
											value={partner.position || ""}
											onChange={(e) =>
												handleInputChange(e, "ansprechpartner", index)
											}
											placeholder="Position/Abteilung"
											className="h-8 text-sm border-gray-600"
										/>
									</TableCell>
									<TableCell className="py-2 px-2">
										<Input
											name="email"
											type="email"
											value={partner.email || ""}
											onChange={(e) =>
												handleInputChange(e, "ansprechpartner", index)
											}
											placeholder="<EMAIL>"
											className="h-8 text-sm border-gray-600"
										/>
									</TableCell>
									<TableCell className="py-2 px-2">
										<Input
											name="telefon"
											type="tel"
											value={partner.telefon || ""}
											onChange={(e) =>
												handleInputChange(e, "ansprechpartner", index)
											}
											placeholder="+49 123 456789"
											className="h-8 text-sm border-gray-600"
										/>
									</TableCell>
									<TableCell className="py-2 px-2">
										<Input
											name="mobil"
											type="tel"
											value={partner.mobil || ""}
											onChange={(e) =>
												handleInputChange(e, "ansprechpartner", index)
											}
											placeholder="+49 123 456789"
											className="h-8 text-sm border-gray-600"
										/>
									</TableCell>
									<TableCell className="py-2 px-2">
										<div className="space-y-1">
											<div className="flex items-center space-x-1">
												<Checkbox
													id={`partner-${index}-anrede`}
													name="istEmailAnrede"
													checked={partner.istEmailAnrede || false}
													onCheckedChange={(checked) => {
														// Ensure only one E-Mail-Anrede is selected
														const updatedAnsprechpartner =
															formState.ansprechpartner.map((a, i) => ({
																...a,
																istEmailAnrede: i === index ? !!checked : false,
															}));
														setFormState((prev) => ({
															...prev,
															ansprechpartner: updatedAnsprechpartner,
														}));
													}}
													className="h-3 w-3"
												/>
												<Label
													htmlFor={`partner-${index}-anrede`}
													className="text-xs text-purple-400"
												>
													Anrede
												</Label>
											</div>
											<div className="flex items-center space-x-1">
												<Checkbox
													id={`partner-${index}-lieferschein`}
													name="istEmailLieferscheinEmpfaenger"
													checked={partner.istEmailLieferscheinEmpfaenger || false}
													onCheckedChange={(checked) =>
														handleInputChange(
															{
																target: {
																	name: "istEmailLieferscheinEmpfaenger",
																	type: "checkbox",
																	checked,
																},
															} as any,
															"ansprechpartner",
															index,
														)
													}
													className="h-3 w-3"
												/>
												<Label
													htmlFor={`partner-${index}-lieferschein`}
													className="text-xs text-blue-400"
												>
													Lieferschein
												</Label>
											</div>
											<div className="flex items-center space-x-1">
												<Checkbox
													id={`partner-${index}-uebersicht`}
													name="istEmailUebersichtEmpfaenger"
													checked={partner.istEmailUebersichtEmpfaenger || false}
													onCheckedChange={(checked) =>
														handleInputChange(
															{
																target: {
																	name: "istEmailUebersichtEmpfaenger",
																	type: "checkbox",
																	checked,
																},
															} as any,
															"ansprechpartner",
															index,
														)
													}
													className="h-3 w-3"
												/>
												<Label
													htmlFor={`partner-${index}-uebersicht`}
													className="text-xs text-green-400"
												>
													Übersicht
												</Label>
											</div>
										</div>
									</TableCell>
									<TableCell className="text-center py-2 px-2">
										{formState.ansprechpartner.length > 1 && (
											<Button
												type="button"
												variant="ghost"
												size="icon"
												onClick={() => removeFromArray("ansprechpartner", index)}
												className="h-6 w-6 text-red-400 hover:text-red-500 hover:bg-red-500/10"
												title="Ansprechpartner löschen"
											>
												<Trash2 className="h-3 w-3" />
											</Button>
										)}
									</TableCell>
								</TableRow>
							))}
						</TableBody>
					</Table>
				</CardContent>
			</Card>
		</form>
	);
}
