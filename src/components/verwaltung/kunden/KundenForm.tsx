import { api } from "@/../convex/_generated/api";
import type { Id } from "@/../convex/_generated/dataModel";
import { Button } from "@/components/_shared/Button";
import { Checkbox } from "@/components/_shared/Checkbox";
import { Input } from "@/components/_shared/Input";
import { Label } from "@/components/_shared/Label";
import { AppDialogLayout } from "@/components/layout/AppDialogLayout";
import { useMutation } from "convex/react";
import { Check, Euro, Pencil, PlusCircle, Trash2, X } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";

// Kunde interface - matches the API return type
interface Kunde {
	_id: Id<"kunden">;
	_creationTime: number;
	name: string;
	stundenpreis: number;
	anfahrtskosten: number;
	standorte: StandortData[];
	ansprechpartner: AnsprechpartnerData[];
}

// Define data structures for form sub-elements
export interface StandortData {
	strasse: string;
	plz: string;
	ort: string;
	land?: string;
	istHauptstandort: boolean;
}

export interface AnsprechpartnerData {
	name: string;
	email?: string;
	telefon?: string;
	mobil?: string;
	position?: string;
	istHauptansprechpartner: boolean;
	istEmailLieferscheinEmpfaenger?: boolean;
	istEmailUebersichtEmpfaenger?: boolean;
	istEmailAnrede?: boolean;
}

// Define the overall form data structure
export interface KundeFormData {
	name: string;
	stundenpreis: string;
	anfahrtskosten: string;
	standorte: StandortData[];
	ansprechpartner: AnsprechpartnerData[];
}

interface KundenFormProps {
	editingKunde: (Kunde & { _id: Id<"kunden"> }) | null; // Use the main Kunde interface
	onSubmitSuccess: () => void;
	onCancel: () => void;
}

const defaultStandort: StandortData = {
	strasse: "",
	plz: "",
	ort: "",
	land: "Deutschland",
	istHauptstandort: false,
};

const defaultAnsprechpartner: AnsprechpartnerData = {
	name: "",
	email: "",
	telefon: "",
	mobil: "",
	position: "",
	istHauptansprechpartner: false,
	istEmailLieferscheinEmpfaenger: false,
	istEmailUebersichtEmpfaenger: false,
	istEmailAnrede: false,
};

export function KundenForm({
	editingKunde,
	onSubmitSuccess,
	onCancel,
}: KundenFormProps) {
	const createKunde = useMutation(api.verwaltung.kunden.create);
	const updateKunde = useMutation(api.verwaltung.kunden.update);

	const [formState, setFormState] = useState<KundeFormData>({
		name: "",
		stundenpreis: "",
		anfahrtskosten: "",
		standorte: [],
		ansprechpartner: [],
	});

	useEffect(() => {
		if (editingKunde) {
			setFormState({
				name: editingKunde.name ?? "",
				stundenpreis: editingKunde.stundenpreis?.toString() ?? "",
				anfahrtskosten: editingKunde.anfahrtskosten?.toString() ?? "",
				standorte: editingKunde.standorte?.map((s: any) => ({
					...s,
					land: s.land ?? "Deutschland",
				})) || [{ ...defaultStandort, istHauptstandort: true }],
				ansprechpartner: editingKunde.ansprechpartner || [
					{ ...defaultAnsprechpartner, istHauptansprechpartner: true },
				],
			});
		} else {
			setFormState({
				name: "",
				stundenpreis: "",
				anfahrtskosten: "",
				standorte: [{ ...defaultStandort, istHauptstandort: true }],
				ansprechpartner: [
					{ ...defaultAnsprechpartner, istHauptansprechpartner: true },
				],
			});
		}
	}, [editingKunde]);

	const handleInputChange = (
		e: React.ChangeEvent<HTMLInputElement>,
		arrayName?: keyof Pick<KundeFormData, "standorte" | "ansprechpartner">,
		index?: number,
	) => {
		const { id, name, value, type, checked } = e.target;
		const propValue = type === "checkbox" ? checked : value;

		if (arrayName && index !== undefined) {
			setFormState((prev) => {
				const newArray = [...prev[arrayName]];
				(newArray[index] as any)[name] = propValue;

				// Handle primary toggling
				if (name === "istHauptstandort" && checked) {
					newArray.forEach((item, i) => {
						if (i !== index) (item as StandortData).istHauptstandort = false;
					});
				}
				if (name === "istHauptansprechpartner" && checked) {
					newArray.forEach((item, i) => {
						if (i !== index)
							(item as AnsprechpartnerData).istHauptansprechpartner = false;
					});
				}
				return { ...prev, [arrayName]: newArray };
			});
		} else {
			setFormState((prev) => ({ ...prev, [id]: propValue }));
		}
	};

	const addToArray = (
		arrayName: keyof Pick<KundeFormData, "standorte" | "ansprechpartner">,
	) => {
		setFormState((prev) => ({
			...prev,
			[arrayName]: [
				...prev[arrayName],
				arrayName === "standorte"
					? {
							...defaultStandort,
							istHauptstandort: prev.standorte.length === 0,
						}
					: {
							...defaultAnsprechpartner,
							istHauptansprechpartner: prev.ansprechpartner.length === 0,
						},
			],
		}));
	};

	const removeFromArray = (
		arrayName: keyof Pick<KundeFormData, "standorte" | "ansprechpartner">,
		index: number,
	) => {
		setFormState((prev) => {
			const newArray = prev[arrayName].filter((_, i) => i !== index);
			// If the removed item was primary and it's now empty or no other primary exists, make the new first item primary.
			if (
				arrayName === "standorte" &&
				newArray.length > 0 &&
				!newArray.some((s) => (s as StandortData).istHauptstandort)
			) {
				(newArray[0] as StandortData).istHauptstandort = true;
			}
			if (
				arrayName === "ansprechpartner" &&
				newArray.length > 0 &&
				!newArray.some(
					(a) => (a as AnsprechpartnerData).istHauptansprechpartner,
				)
			) {
				(newArray[0] as AnsprechpartnerData).istHauptansprechpartner = true;
			}
			return { ...prev, [arrayName]: newArray };
		});
	};

	const handleSubmit = () => {
		const { name, stundenpreis, anfahrtskosten, standorte, ansprechpartner } =
			formState;

		if (!name || !stundenpreis || !anfahrtskosten) {
			toast.error(
				"Bitte Firmenname, Stundenpreis und Anfahrtskosten ausfüllen.",
			);
			return;
		}
		if (standorte.length === 0) {
			toast.error("Bitte mindestens einen Standort hinzufügen.");
			return;
		}
		if (ansprechpartner.length === 0) {
			toast.error("Bitte mindestens einen Ansprechpartner hinzufügen.");
			return;
		}
		if (!standorte.some((s) => s.istHauptstandort)) {
			toast.error("Bitte einen Hauptstandort auswählen.");
			return;
		}
		if (!ansprechpartner.some((a) => a.istHauptansprechpartner)) {
			toast.error("Bitte einen Hauptansprechpartner auswählen.");
			return;
		}
		const emailAnredeCount = ansprechpartner.filter(
			(a) => a.istEmailAnrede,
		).length;
		if (emailAnredeCount > 1) {
			toast.error(
				"Nur ein Ansprechpartner kann als E-Mail-Anrede ausgewählt werden.",
			);
			return;
		}

		try {
			const parsedStundenpreis = Number.parseFloat(stundenpreis);
			const parsedAnfahrtskosten = Number.parseFloat(anfahrtskosten);

			if (
				Number.isNaN(parsedStundenpreis) ||
				Number.isNaN(parsedAnfahrtskosten)
			) {
				toast.error("Bitte gültige Zahlen für Preise eingeben.");
				return;
			}

			const mutationArgs = {
				name,
				stundenpreis: parsedStundenpreis,
				anfahrtskosten: parsedAnfahrtskosten,
				standorte: standorte.map((s) => ({ ...s, land: s.land || undefined })), // Ensure optional fields are handled
				ansprechpartner: ansprechpartner.map((a) => ({
					...a,
					email: a.email || undefined,
					telefon: a.telefon || undefined,
					mobil: a.mobil || undefined,
					position: a.position || undefined,
					istEmailAnrede: a.istEmailAnrede || false,
					istEmailLieferscheinEmpfaenger:
						a.istEmailLieferscheinEmpfaenger || false,
					istEmailUebersichtEmpfaenger: a.istEmailUebersichtEmpfaenger || false,
				})), // Added mobil handling
			};

			const promise = editingKunde
				? updateKunde({ id: editingKunde._id, ...mutationArgs })
				: createKunde(mutationArgs);

			promise
				.then(() => {
					toast.success(
						`Kunde erfolgreich ${editingKunde ? "aktualisiert" : "angelegt"}`,
					);
					onSubmitSuccess();
				})
				.catch((error: Error) => {
					toast.error(
						`Fehler beim Speichern: ${String(error.message || error)}`,
					);
				});
		} catch (error) {
			toast.error("Fehler bei der Verarbeitung der Eingabe.");
		}
	};

	return (
		<AppDialogLayout
			isOpen={true}
			onClose={onCancel}
			title={editingKunde ? "Kunde bearbeiten" : "Neuen Kunden anlegen"}
			icon={
				editingKunde ? (
					<Pencil className="h-3.5 w-3.5" />
				) : (
					<PlusCircle className="h-3.5 w-3.5" />
				)
			}
			footerAction={{
				label: editingKunde ? "Aktualisieren" : "Speichern",
				onClick: handleSubmit,
				icon: <Check className="h-4 w-4" />,
			}}
			maxWidth="2xl"
		>
			<div className="space-y-4">
				{/* Stammdaten */}
				<div>
					<h4 className="text-sm font-medium text-gray-300 mb-2">
						Allgemeine Kundendaten
					</h4>
					<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
						<div className="md:col-span-1">
							<Label
								htmlFor="name"
								className="text-xs text-gray-400 mb-1 block"
							>
								Firmenname *
							</Label>
							<Input
								id="name"
								value={formState.name}
								onChange={handleInputChange}
								required
								placeholder="Firmenname"
								className="h-9 bg-gray-800/60 border-gray-700"
							/>
						</div>
						<div>
							<Label
								htmlFor="stundenpreis"
								className="text-xs text-gray-400 mb-1 block"
							>
								Stundenpreis (€) *
							</Label>
							<div className="relative">
								<Input
									id="stundenpreis"
									type="number"
									value={formState.stundenpreis}
									onChange={handleInputChange}
									required
									min="0"
									step="0.01"
									placeholder="0.00"
									className="h-9 bg-gray-800/60 border-gray-700 pl-9"
								/>
								<Euro className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
							</div>
						</div>
						<div>
							<Label
								htmlFor="anfahrtskosten"
								className="text-xs text-gray-400 mb-1 block"
							>
								Anfahrtskosten (€) *
							</Label>
							<div className="relative">
								<Input
									id="anfahrtskosten"
									type="number"
									value={formState.anfahrtskosten}
									onChange={handleInputChange}
									required
									min="0"
									step="0.01"
									placeholder="0.00"
									className="h-9 bg-gray-800/60 border-gray-700 pl-9"
								/>
								<Euro className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
							</div>
						</div>
					</div>
				</div>

				{/* Standorte */}
				<div className="space-y-2 pt-2 border-t border-gray-700/30">
					<div className="flex justify-between items-center">
						<h4 className="text-sm font-medium text-gray-300">Standorte</h4>
						<Button
							type="button"
							variant="outline"
							size="sm"
							onClick={() => addToArray("standorte")}
							className="gap-1"
						>
							<PlusCircle className="h-3.5 w-3.5" /> Standort
						</Button>
					</div>
					{formState.standorte.map((standort, index) => (
						<div
							key={`standort-${index}`}
							className="p-2 border border-gray-700/50 rounded-md space-y-2 bg-gray-800/30"
						>
							<div className="grid grid-cols-1 md:grid-cols-5 gap-2 items-end">
								<div className="md:col-span-2">
									<Label
										htmlFor={`standort-strasse-${index}`}
										className="text-xs text-gray-400 mb-0.5 block"
									>
										Straße
									</Label>
									<Input
										id={`standort-strasse-${index}`}
										name="strasse"
										value={standort.strasse}
										onChange={(e) => handleInputChange(e, "standorte", index)}
										placeholder="Musterstraße 1"
										className="h-8 text-sm"
									/>
								</div>
								<div>
									<Label
										htmlFor={`standort-plz-${index}`}
										className="text-xs text-gray-400 mb-0.5 block"
									>
										PLZ
									</Label>
									<Input
										id={`standort-plz-${index}`}
										name="plz"
										value={standort.plz}
										onChange={(e) => handleInputChange(e, "standorte", index)}
										placeholder="12345"
										className="h-8 text-sm"
									/>
								</div>
								<div>
									<Label
										htmlFor={`standort-ort-${index}`}
										className="text-xs text-gray-400 mb-0.5 block"
									>
										Ort
									</Label>
									<Input
										id={`standort-ort-${index}`}
										name="ort"
										value={standort.ort}
										onChange={(e) => handleInputChange(e, "standorte", index)}
										placeholder="Musterstadt"
										className="h-8 text-sm"
									/>
								</div>
								<div className="flex items-center justify-between md:justify-end gap-2">
									<div className="flex items-center space-x-1.5">
										<Checkbox
											id={`standort-istHauptstandort-${index}`}
											name="istHauptstandort"
											checked={standort.istHauptstandort}
											onCheckedChange={(checked) =>
												handleInputChange(
													{
														target: {
															id: `standort-istHauptstandort-${index}`,
															name: "istHauptstandort",
															value: "",
															type: "checkbox",
															checked,
														},
													} as any,
													"standorte",
													index,
												)
											}
										/>
										<Label
											htmlFor={`standort-istHauptstandort-${index}`}
											className="text-xs text-gray-300"
										>
											Haupt
										</Label>
									</div>
									{formState.standorte.length > 1 && (
										<Button
											type="button"
											variant="ghost"
											size="icon"
											onClick={() => removeFromArray("standorte", index)}
											className="h-7 w-7 text-red-400 hover:bg-red-500/20"
										>
											<Trash2 className="h-3.5 w-3.5" />
										</Button>
									)}
								</div>
							</div>
						</div>
					))}
				</div>

				{/* Ansprechpartner */}
				<div className="space-y-2 pt-2 border-t border-gray-700/30">
					<div className="flex justify-between items-center">
						<h4 className="text-sm font-medium text-gray-300">
							Ansprechpartner
						</h4>
						<Button
							type="button"
							variant="outline"
							size="sm"
							onClick={() => addToArray("ansprechpartner")}
							className="gap-1"
						>
							<PlusCircle className="h-3.5 w-3.5" /> Ansprechpartner
						</Button>
					</div>
					{formState.ansprechpartner.map((ap, index) => (
						<div
							key={`ansprechpartner-${index}`}
							className="p-2 border border-gray-700/50 rounded-md space-y-2 bg-gray-800/30"
						>
							<div className="grid grid-cols-1 md:grid-cols-6 gap-2 items-end">
								<div>
									<Label
										htmlFor={`ansprechpartner-name-${index}`}
										className="text-xs text-gray-400 mb-0.5 block"
									>
										Name
									</Label>
									<Input
										id={`ansprechpartner-name-${index}`}
										name="name"
										value={ap.name}
										onChange={(e) =>
											handleInputChange(e, "ansprechpartner", index)
										}
										placeholder="Max Mustermann"
										className="h-8 text-sm"
									/>
								</div>
								<div>
									<Label
										htmlFor={`ansprechpartner-email-${index}`}
										className="text-xs text-gray-400 mb-0.5 block"
									>
										E-Mail
									</Label>
									<Input
										id={`ansprechpartner-email-${index}`}
										name="email"
										type="email"
										value={ap.email}
										onChange={(e) =>
											handleInputChange(e, "ansprechpartner", index)
										}
										placeholder="<EMAIL>"
										className="h-8 text-sm"
									/>
								</div>
								<div>
									<Label
										htmlFor={`ansprechpartner-telefon-${index}`}
										className="text-xs text-gray-400 mb-0.5 block"
									>
										Telefon
									</Label>
									<Input
										id={`ansprechpartner-telefon-${index}`}
										name="telefon"
										value={ap.telefon}
										onChange={(e) =>
											handleInputChange(e, "ansprechpartner", index)
										}
										placeholder="0123 456789"
										className="h-8 text-sm"
									/>
								</div>
								<div>
									<Label
										htmlFor={`ansprechpartner-mobil-${index}`}
										className="text-xs text-gray-400 mb-0.5 block"
									>
										Mobil
									</Label>
									<Input
										id={`ansprechpartner-mobil-${index}`}
										name="mobil"
										value={ap.mobil}
										onChange={(e) =>
											handleInputChange(e, "ansprechpartner", index)
										}
										placeholder="0171 987654"
										className="h-8 text-sm"
									/>
								</div>
								<div>
									<Label
										htmlFor={`ansprechpartner-position-${index}`}
										className="text-xs text-gray-400 mb-0.5 block"
									>
										Position
									</Label>
									<Input
										id={`ansprechpartner-position-${index}`}
										name="position"
										value={ap.position}
										onChange={(e) =>
											handleInputChange(e, "ansprechpartner", index)
										}
										placeholder="Geschäftsführer"
										className="h-8 text-sm"
									/>
								</div>
								<div className="flex items-center justify-between md:justify-end gap-2">
									<div className="flex items-center space-x-1.5">
										<Checkbox
											id={`ansprechpartner-istHauptansprechpartner-${index}`}
											name="istHauptansprechpartner"
											checked={ap.istHauptansprechpartner}
											onCheckedChange={(checked) =>
												handleInputChange(
													{
														target: {
															id: `ansprechpartner-istHauptansprechpartner-${index}`,
															name: "istHauptansprechpartner",
															value: "",
															type: "checkbox",
															checked,
														},
													} as any,
													"ansprechpartner",
													index,
												)
											}
										/>
										<Label
											htmlFor={`ansprechpartner-istHauptansprechpartner-${index}`}
											className="text-xs text-gray-300"
										>
											Haupt
										</Label>
									</div>
									<div className="flex items-center space-x-1.5">
										<Checkbox
											id={`ansprechpartner-istEmailLieferscheinEmpfaenger-${index}`}
											name="istEmailLieferscheinEmpfaenger"
											checked={ap.istEmailLieferscheinEmpfaenger || false}
											onCheckedChange={(checked) =>
												handleInputChange(
													{
														target: {
															id: `ansprechpartner-istEmailLieferscheinEmpfaenger-${index}`,
															name: "istEmailLieferscheinEmpfaenger",
															value: "",
															type: "checkbox",
															checked,
														},
													} as any,
													"ansprechpartner",
													index,
												)
											}
										/>
										<Label
											htmlFor={`ansprechpartner-istEmailLieferscheinEmpfaenger-${index}`}
											className="text-xs text-gray-300"
										>
											Lieferschein-Empfänger
										</Label>
									</div>
									<div className="flex items-center space-x-1.5">
										<Checkbox
											id={`ansprechpartner-istEmailUebersichtEmpfaenger-${index}`}
											name="istEmailUebersichtEmpfaenger"
											checked={ap.istEmailUebersichtEmpfaenger || false}
											onCheckedChange={(checked) =>
												handleInputChange(
													{
														target: {
															id: `ansprechpartner-istEmailUebersichtEmpfaenger-${index}`,
															name: "istEmailUebersichtEmpfaenger",
															value: "",
															type: "checkbox",
															checked,
														},
													} as any,
													"ansprechpartner",
													index,
												)
											}
										/>
										<Label
											htmlFor={`ansprechpartner-istEmailUebersichtEmpfaenger-${index}`}
											className="text-xs text-gray-300"
										>
											Übersicht-Empfänger
										</Label>
									</div>
									<div className="flex items-center space-x-1.5">
										<Checkbox
											id={`ansprechpartner-istEmailAnrede-${index}`}
											name="istEmailAnrede"
											checked={ap.istEmailAnrede || false}
											onCheckedChange={(checked) => {
												// Ensure only one E-Mail-Anrede is selected
												const updatedAnsprechpartner =
													formState.ansprechpartner.map((a, i) => ({
														...a,
														istEmailAnrede: i === index ? !!checked : false,
													}));
												setFormState((prev) => ({
													...prev,
													ansprechpartner: updatedAnsprechpartner,
												}));
											}}
										/>
										<Label
											htmlFor={`ansprechpartner-istEmailAnrede-${index}`}
											className="text-xs text-gray-300"
										>
											E-Mail Anrede
										</Label>
									</div>
									{formState.ansprechpartner.length > 1 && (
										<Button
											type="button"
											variant="ghost"
											size="icon"
											onClick={() => removeFromArray("ansprechpartner", index)}
											className="h-7 w-7 text-red-400 hover:bg-red-500/20"
										>
											<Trash2 className="h-3.5 w-3.5" />
										</Button>
									)}
								</div>
							</div>
						</div>
					))}
				</div>
			</div>
		</AppDialogLayout>
	);
}
