import * as React from "react";
import { cn } from "../../lib/utils/cn";

import { Slot } from "@radix-ui/react-slot";

export interface ButtonProps
	extends React.ButtonHTMLAttributes<HTMLButtonElement> {
	asChild?: boolean;
	variant?:
		| "default"
		| "destructive"
		| "outline"
		| "secondary"
		| "ghost"
		| "link";
	size?: "default" | "sm" | "lg" | "icon";
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
	(
		{
			className,
			variant = "default",
			size = "default",
			asChild = false,
			...props
		},
		ref,
	) => {
		const Comp = asChild ? Slot : "button";
		return (
			<Comp
				className={cn(
					"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-white transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
					{
						"bg-blue-500 text-white hover:bg-blue-600": variant === "default",
						"bg-red-500 text-white hover:bg-red-600": variant === "destructive",
						"border border-gray-700 bg-transparent hover:bg-gray-800 hover:text-gray-100":
							variant === "outline",
						"bg-gray-700 text-gray-100 hover:bg-gray-600":
							variant === "secondary",
						"hover:bg-gray-800 hover:text-gray-100": variant === "ghost",
						"text-blue-500 underline-offset-4 hover:underline":
							variant === "link",
						"h-10 px-4 py-2": size === "default",
						"h-9 rounded-md px-3": size === "sm",
						"h-11 rounded-md px-8": size === "lg",
						"h-10 w-10": size === "icon",
					},
					className,
				)}
				ref={ref}
				{...props}
			/>
		);
	},
);
Button.displayName = "Button";

export { Button };
