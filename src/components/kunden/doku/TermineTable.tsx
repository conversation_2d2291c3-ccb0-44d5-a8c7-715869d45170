import type { Doc, Id } from "@/../convex/_generated/dataModel";
import { Button } from "@/components/_shared/Button";
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
} from "@/components/_shared/Card";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/_shared/Table";
import {
	calculateTimeRemaining,
	formatDate,
	formatTime,
} from "@/lib/utils/dateUtils";
import { Calendar, Repeat } from "lucide-react";
import { Link } from "react-router-dom";

interface TerminData {
	_id: Id<"kunden_termine">;
	titel: string;
	kategorie: string;
	datum?: string;
	uhrzeit?: string;
	istWiederholend: boolean;
	naechsteWiederholung?: string;
}

interface TermineTableProps {
	termine: TerminData[];
	kundenId: Id<"kunden">;
}

export function TermineTable({ termine, kundenId }: TermineTableProps) {
	return (
		<Card className="shadow-lg border-0 overflow-hidden">
			<CardHeader className="pb-2.5 px-4 border-b border-gray-700/50 flex flex-row justify-between items-center">
				<CardTitle className="text-base font-medium">Termine</CardTitle>
				<Link to={`/kunden/termine/${kundenId}`}>
					<Button
						variant="ghost"
						size="icon"
						className="h-6 w-6 text-gray-400 hover:text-white hover:bg-gray-700/70"
						title="Alle Termine verwalten"
					>
						<Calendar className="h-3 w-3" />
					</Button>
				</Link>
			</CardHeader>
			<CardContent className="p-0">
				{termine && termine.length > 0 ? (
					<Table>
						<TableHeader>
							<TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
								<TableHead className="py-1.5 px-2.5 text-xs">
									Kategorie
								</TableHead>
								<TableHead className="py-1.5 px-2.5 text-xs">Titel</TableHead>
								<TableHead className="py-1.5 px-2.5 text-xs">Datum</TableHead>
								<TableHead className="py-1.5 px-2.5 text-xs">Uhrzeit</TableHead>
								<TableHead className="py-1.5 px-2.5 text-xs">
									Restlaufzeit
								</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{termine.slice(0, 5).map((termin) => {
								const timeRemaining = calculateTimeRemaining(
									termin.istWiederholend
										? termin.naechsteWiederholung
										: termin.datum,
									termin.uhrzeit || "",
								);

								return (
									<TableRow key={termin._id}>
										<TableCell className="py-1 px-2.5 text-xs">
											<span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs bg-blue-500/10 text-blue-400">
												{termin.kategorie}
											</span>
										</TableCell>
										<TableCell className="py-1 px-2.5 text-xs font-medium">
											<div className="flex items-center gap-1">
												{termin.titel}
												{termin.istWiederholend && (
													<div
														className="h-3 w-3 text-blue-400"
														title="Wiederholender Termin"
													>
														<Repeat className="h-3 w-3" />
													</div>
												)}
											</div>
										</TableCell>
										<TableCell className="py-1 px-2.5 text-xs">
											{termin.istWiederholend
												? formatDate(termin.naechsteWiederholung || "")
												: formatDate(termin.datum || "")}
										</TableCell>
										<TableCell className="py-1 px-2.5 text-xs">
											{termin.uhrzeit ? formatTime(termin.uhrzeit) : "-"}
										</TableCell>
										<TableCell className="py-1 px-2.5 text-xs">
											<span
												className={
													timeRemaining.startsWith("vor")
														? "text-red-400"
														: "text-green-400"
												}
											>
												{timeRemaining}
											</span>
										</TableCell>
									</TableRow>
								);
							})}
						</TableBody>
					</Table>
				) : (
					<p className="text-gray-400 p-4">Keine Termine vorhanden.</p>
				)}
			</CardContent>
		</Card>
	);
}
