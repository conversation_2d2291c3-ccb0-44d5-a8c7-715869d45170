import type { Doc } from "@/../convex/_generated/dataModel";
import { Button } from "@/components/_shared/Button";
import {
	<PERSON>,
	CardContent,
	CardHeader,
	CardTitle,
} from "@/components/_shared/Card";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/_shared/Table";
import { Check, Copy, MapPin } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

interface StandorteTableProps {
	kunde: Doc<"kunden">;
}

export function StandorteTable({ kunde }: StandorteTableProps) {
	const [copiedStates, setCopiedStates] = useState<Record<string, boolean>>({});

	const copyToClipboard = (text: string, id: string) => {
		navigator.clipboard.writeText(text).then(
			() => {
				setCopiedStates((prev) => ({ ...prev, [id]: true }));
				toast.success("In Zwischenablage kopiert!");
				setTimeout(
					() => setCopiedStates((prev) => ({ ...prev, [id]: false })),
					1500,
				);
			},
			(err) => {
				toast.error("<PERSON><PERSON> beim Kopieren.");
			},
		);
	};

	return (
		<Card className="shadow-lg border-0 overflow-hidden">
			<CardHeader className="pb-2.5 px-4 border-b border-gray-700/50">
				<CardTitle className="text-base font-medium">
					Standorte (aus Stammdaten)
				</CardTitle>
			</CardHeader>
			<CardContent className="p-0">
				{kunde.standorte && kunde.standorte.length > 0 ? (
					<Table>
						<TableHeader>
							<TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
								<TableHead className="w-12 text-center py-1.5 px-2.5 text-xs">
									Haupt
								</TableHead>
								<TableHead className="py-1.5 px-2.5 text-xs">Straße</TableHead>
								<TableHead className="py-1.5 px-2.5 text-xs">PLZ</TableHead>
								<TableHead className="py-1.5 px-2.5 text-xs">Ort</TableHead>
								<TableHead className="py-1.5 px-2.5 text-xs">Land</TableHead>
								<TableHead className="w-20 text-center py-1.5 px-2.5 text-xs">
									Aktionen
								</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{kunde.standorte.map((standort, index) => {
								const addressString = `${standort.strasse}, ${standort.plz} ${standort.ort}${standort.land ? `, ${standort.land}` : ""}`;
								const mapsUrl = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(addressString)}`;
								const copyId = `standort-${index}`;
								return (
									<TableRow key={index}>
										<TableCell className="text-center py-1 px-2.5 text-xs">
											{standort.istHauptstandort && (
												<Check className="h-4 w-4 text-green-400 mx-auto" />
											)}
										</TableCell>
										<TableCell className="py-1 px-2.5 text-xs">
											{standort.strasse}
										</TableCell>
										<TableCell className="py-1 px-2.5 text-xs">
											{standort.plz}
										</TableCell>
										<TableCell className="py-1 px-2.5 text-xs">
											{standort.ort}
										</TableCell>
										<TableCell className="py-1 px-2.5 text-xs">
											{standort.land || "-"}
										</TableCell>
										<TableCell className="text-center py-1 px-2.5 text-xs">
											<div className="flex justify-center gap-1">
												<Button
													variant="ghost"
													size="icon"
													onClick={() => copyToClipboard(addressString, copyId)}
													className={`h-6 w-6 ${copiedStates[copyId] ? "text-green-400" : "text-blue-400 hover:text-blue-500"} hover:bg-blue-500/10`}
													title="Adresse kopieren"
												>
													<Copy className="h-3 w-3" />
												</Button>
												<Button
													variant="ghost"
													size="icon"
													onClick={() => window.open(mapsUrl, "_blank")}
													className="h-6 w-6 text-purple-400 hover:text-purple-500 hover:bg-purple-500/10"
													title="In Karte öffnen"
												>
													<MapPin className="h-3 w-3" />
												</Button>
											</div>
										</TableCell>
									</TableRow>
								);
							})}
						</TableBody>
					</Table>
				) : (
					<p className="text-gray-400 p-4">
						Keine Standorte in den Stammdaten hinterlegt.
					</p>
				)}
			</CardContent>
		</Card>
	);
}
