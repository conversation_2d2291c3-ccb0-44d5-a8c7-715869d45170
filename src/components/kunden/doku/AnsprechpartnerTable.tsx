import type { Doc } from "@/../convex/_generated/dataModel";
import { Button } from "@/components/_shared/Button";
import {
	<PERSON>,
	CardContent,
	CardHeader,
	CardTitle,
} from "@/components/_shared/Card";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/_shared/Table";
import type { AnsprechpartnerData } from "@/components/verwaltung/kunden/KundenForm";
import { Check, Mail, QrCode } from "lucide-react";
import { useState } from "react";
import { QRCodeModal } from "./QRCodeModal";

interface AnsprechpartnerTableProps {
	kunde: Doc<"kunden">;
}

export function AnsprechpartnerTable({ kunde }: AnsprechpartnerTableProps) {
	const [qrModalState, setQrModalState] = useState<{
		isOpen: boolean;
		partner: AnsprechpartnerData | null;
	}>({ isOpen: false, partner: null });

	const handleShowQRCode = (partner: AnsprechpartnerData) => {
		setQrModalState({ isOpen: true, partner });
	};

	const handleCloseQRModal = () => {
		setQrModalState({ isOpen: false, partner: null });
	};

	return (
		<>
			<Card className="shadow-lg border-0 overflow-hidden">
				<CardHeader className="pb-2.5 px-4 border-b border-gray-700/50">
					<CardTitle className="text-base font-medium">
						Ansprechpartner (aus Stammdaten)
					</CardTitle>
				</CardHeader>
				<CardContent className="p-0">
					{kunde.ansprechpartner && kunde.ansprechpartner.length > 0 ? (
						<Table>
							<TableHeader>
								<TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
									<TableHead className="w-16 text-center py-1.5 px-2.5 text-xs">
										E-Mail
									</TableHead>
									<TableHead className="w-12 text-center py-1.5 px-2.5 text-xs">
										Haupt
									</TableHead>
									<TableHead className="py-1.5 px-2.5 text-xs">Name</TableHead>
									<TableHead className="py-1.5 px-2.5 text-xs">
										Position
									</TableHead>
									<TableHead className="py-1.5 px-2.5 text-xs">
										E-Mail
									</TableHead>
									<TableHead className="py-1.5 px-2.5 text-xs">
										Telefon
									</TableHead>
									<TableHead className="py-1.5 px-2.5 text-xs">Mobil</TableHead>
									<TableHead className="w-16 text-center py-1.5 px-2.5 text-xs">
										Aktionen
									</TableHead>
								</TableRow>
							</TableHeader>
							<TableBody>
								{kunde.ansprechpartner.map((partner, index) => {
									return (
										<TableRow key={index}>
											<TableCell className="text-center py-1 px-2.5 text-xs">
												<div className="flex gap-0.5 justify-center">
													{partner.istEmailLieferscheinEmpfaenger && (
														<div
															className="h-3 w-3 text-blue-400"
															title="Lieferschein-Empfänger"
														>
															<Mail className="h-3 w-3" />
														</div>
													)}
													{partner.istEmailUebersichtEmpfaenger && (
														<div
															className="h-3 w-3 text-green-400"
															title="Übersicht-Empfänger"
														>
															<Mail className="h-3 w-3" />
														</div>
													)}
													{partner.istEmailAnrede && (
														<div
															className="h-3 w-3 text-purple-400"
															title="E-Mail-Anrede"
														>
															<Mail className="h-3 w-3" />
														</div>
													)}
												</div>
											</TableCell>
											<TableCell className="text-center py-1 px-2.5 text-xs">
												{partner.istHauptansprechpartner && (
													<Check className="h-4 w-4 text-green-400 mx-auto" />
												)}
											</TableCell>
											<TableCell className="py-1 px-2.5 text-xs">
												{partner.name}
											</TableCell>
											<TableCell className="py-1 px-2.5 text-xs">
												{partner.position || "-"}
											</TableCell>
											<TableCell className="py-1 px-2.5 text-xs">
												{partner.email ? (
													<a
														href={`mailto:${partner.email}`}
														className="text-cyan-400 hover:text-cyan-300"
													>
														{partner.email}
													</a>
												) : (
													"-"
												)}
											</TableCell>
											<TableCell className="py-1 px-2.5 text-xs">
												{partner.telefon || "-"}
											</TableCell>
											<TableCell className="py-1 px-2.5 text-xs">
												{partner.mobil || "-"}
											</TableCell>
											<TableCell className="text-center py-1 px-2.5 text-xs">
												<div className="flex justify-center gap-1">
													<Button
														variant="ghost"
														size="icon"
														onClick={() => handleShowQRCode(partner)}
														className="h-6 w-6 text-green-400 hover:text-green-500 hover:bg-green-500/10"
														title="QR-Code anzeigen"
													>
														<QrCode className="h-3 w-3" />
													</Button>
												</div>
											</TableCell>
										</TableRow>
									);
								})}
							</TableBody>
						</Table>
					) : (
						<p className="text-gray-400 p-4">
							Keine Ansprechpartner in den Stammdaten hinterlegt.
						</p>
					)}
				</CardContent>
			</Card>

			{qrModalState.isOpen && qrModalState.partner && (
				<QRCodeModal
					isOpen={qrModalState.isOpen}
					onClose={handleCloseQRModal}
					partner={qrModalState.partner}
					kunde={kunde}
				/>
			)}
		</>
	);
}
