import type { Doc } from "@/../convex/_generated/dataModel";
import {
	type FilterConfig,
	type FilterOption,
	GenericFilterControls,
} from "@/components/layout/GenericFilterControls";

interface TermineFilter {
	kundeId: string;
	zeitraum: string; // "7", "14", "30", "90", "365"
}

type KundeDoc = Doc<"kunden">;

interface TermineFilterControlsProps {
	filter: TermineFilter;
	kunden: KundeDoc[];
	onFilterChange: (field: keyof TermineFilter, value: string) => void;
	onResetAllFilters?: () => void;
}

export function TermineFilterControls({
	filter,
	kunden,
	onFilterChange,
	onResetAllFilters,
}: TermineFilterControlsProps) {
	const kundenOptions: FilterOption[] = [
		{ value: "__ALL__", label: "Alle Kunden" },
		...kunden.map((kunde) => ({
			value: kunde._id,
			label: kunde.name,
		})),
	];

	const zeitraumOptions: FilterOption[] = [
		{ value: "7", label: "7 Tage" },
		{ value: "14", label: "14 Tage" },
		{ value: "30", label: "30 Tage" },
		{ value: "90", label: "90 Tage" },
		{ value: "365", label: "365 Tage" },
	];

	const filtersConfig: FilterConfig[] = [
		{
			type: "select",
			id: "termineKundeFilter",
			value: filter.kundeId || "__ALL__",
			onChange: (value) =>
				onFilterChange("kundeId", value === "__ALL__" ? "" : value),
			options: kundenOptions,
			placeholder: "Alle Kunden",
			triggerClassName: "h-8 text-xs bg-gray-800 border-gray-700",
			minWidth: "150px",
		},
		{
			type: "select",
			id: "termineZeitraumFilter",
			value: filter.zeitraum,
			onChange: (value) => onFilterChange("zeitraum", value),
			options: zeitraumOptions,
			placeholder: "Zeitraum wählen",
			triggerClassName: "h-8 text-xs bg-gray-800 border-gray-700",
			minWidth: "130px",
		},
	];

	return (
		<GenericFilterControls
			filters={filtersConfig}
			onResetAll={onResetAllFilters}
		/>
	);
}
