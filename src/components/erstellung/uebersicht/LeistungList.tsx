import { Id } from "@/../convex/_generated/dataModel";
import {
	<PERSON>,
	CardContent,
	CardHeader,
	CardTitle,
} from "@/components/_shared/Card";
import { Checkbox } from "@/components/_shared/Checkbox";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/_shared/Table";
import { formatKontingentNames } from "@/lib/utils/formatUtils";
import { LeistungMitNamen } from "@/pages/erstellung/uebersicht/types";
import {
	Briefcase,
	CalendarDays,
	ChevronDown,
	ChevronUp,
	Clock,
	UserCircle,
} from "lucide-react";
import { memo, useState } from "react";

interface LeistungListProps {
	leistungen: LeistungMitNamen[];
	selectedLeistungen: Id<"kunden_leistungen">[];
	onSelectionChange: (id: Id<"kunden_leistungen">, selected: boolean) => void;
	onSelectAll: (selected: boolean) => void;
	formatDate: (timestamp: number) => string;
	formatTime: (timestamp: number) => string;
	formatHours: (hours: number) => string;
	formatCurrency: (amount: number) => string;
}

const LeistungList = memo(function LeistungList({
	leistungen,
	selectedLeistungen,
	onSelectionChange,
	onSelectAll,
	formatDate,
	formatTime,
	formatHours,
	formatCurrency,
}: LeistungListProps) {
	const [expandedDescriptions, setExpandedDescriptions] = useState<
		Record<Id<"kunden_leistungen">, boolean>
	>({});

	const toggleDescription = (id: Id<"kunden_leistungen">) => {
		setExpandedDescriptions((prev) => ({
			...prev,
			[id]: !prev[id],
		}));
	};

	const allSelected =
		leistungen.length > 0 && selectedLeistungen.length === leistungen.length;

	return (
		<Card className="shadow-lg border-0">
			<CardHeader className="border-b border-gray-700/50 p-4">
				<CardTitle className="text-lg font-medium">
					Leistungsübersicht
				</CardTitle>
			</CardHeader>
			<CardContent className="p-0">
				<div className="overflow-x-auto">
					<Table>
						<TableHeader>
							<TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
								<TableHead className="w-12 text-center">
									<Checkbox
										checked={allSelected}
										onCheckedChange={(checked) => onSelectAll(!!checked)}
										aria-label="Alle auswählen"
									/>
								</TableHead>
								<TableHead className="font-medium w-[15%]">
									Datum / Kontingent
								</TableHead>
								<TableHead className="font-medium w-[15%]">
									Zeitraum / Art
								</TableHead>
								<TableHead className="font-medium w-[20%]">
									Mitarbeiter / Stunden
								</TableHead>
								<TableHead className="font-medium">Beschreibung</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{leistungen.map((leistung) => (
								<TableRow
									key={leistung._id}
									className="border-b border-gray-800"
								>
									<TableCell className="text-center">
										<Checkbox
											checked={selectedLeistungen.includes(leistung._id)}
											onCheckedChange={(checked) =>
												onSelectionChange(leistung._id, !!checked)
											}
											aria-label={`Leistung vom ${formatDate(leistung.startZeit)} auswählen`}
										/>
									</TableCell>
									<TableCell>
										<div className="flex flex-col">
											<div className="flex items-center gap-1.5 font-medium">
												<CalendarDays className="h-3.5 w-3.5 text-gray-400" />
												{formatDate(leistung.startZeit)}
											</div>
											<div className="text-xs text-gray-400 mt-1 flex items-center gap-1.5">
												<Briefcase className="h-3.5 w-3.5" />
												{formatKontingentNames(
													leistung.kontingentName,
													leistung.kontingentName2,
												)}
											</div>
										</div>
									</TableCell>
									<TableCell>
										<div className="flex flex-col">
											<div className="flex items-center gap-1.5">
												<Clock className="h-3.5 w-3.5 text-gray-400" />
												{formatTime(leistung.startZeit)} -{" "}
												{formatTime(leistung.endZeit)}
											</div>
											<div className="text-xs mt-1 inline-flex items-center">
												<span
													className={`px-1.5 py-0.5 rounded-sm text-[10px] uppercase font-medium ${
														leistung.art === "remote"
															? "bg-blue-500/20 text-blue-300"
															: leistung.art === "vor-Ort"
																? "bg-orange-500/20 text-orange-300"
																: "bg-green-500/20 text-green-300"
													}`}
												>
													{leistung.art}
												</span>
											</div>
										</div>
									</TableCell>
									<TableCell>
										<div className="flex flex-col">
											<div className="flex items-center gap-1.5">
												<UserCircle className="h-3.5 w-3.5 text-gray-400" />
												<span>{leistung.mitarbeiterName}</span>
											</div>
											<div className="text-xs text-gray-400 mt-1 flex items-center gap-1.5">
												<Clock className="h-3.5 w-3.5" />
												<span>{formatHours(leistung.stunden)}</span>
											</div>
										</div>
									</TableCell>
									<TableCell>
										<div className="flex items-center justify-between">
											<div
												className={`${
													expandedDescriptions[leistung._id]
														? ""
														: "line-clamp-1"
												}`}
											>
												{leistung.beschreibung}
											</div>
											<button
												onClick={() => toggleDescription(leistung._id)}
												className="ml-2 text-gray-400 hover:text-white"
												aria-label="Beschreibung ein-/ausklappen"
											>
												{expandedDescriptions[leistung._id] ? (
													<ChevronUp className="h-4 w-4" />
												) : (
													<ChevronDown className="h-4 w-4" />
												)}
											</button>
										</div>
									</TableCell>
								</TableRow>
							))}
						</TableBody>
					</Table>
				</div>
			</CardContent>
		</Card>
	);
});

export default LeistungList;
