import { ReactNode, useState } from "react";

interface TabViewProps {
	tabs: {
		id: string;
		title: string;
		content: ReactNode;
	}[];
	defaultTab?: string;
}

export function TabView({ tabs, defaultTab }: TabViewProps) {
	const [activeTab, setActiveTab] = useState<string>(defaultTab || tabs[0].id);

	return (
		<div className="flex flex-col">
			<div className="flex border-b border-gray-800">
				{tabs.map((tab) => (
					<button
						key={tab.id}
						className={`px-3 py-1.5 text-sm font-medium transition-colors ${
							activeTab === tab.id
								? "border-b-2 border-blue-500 text-blue-400"
								: "text-gray-400 hover:text-gray-300"
						}`}
						onClick={() => setActiveTab(tab.id)}
					>
						{tab.title}
					</button>
				))}
			</div>
			<div className="py-2">
				{tabs.find((tab) => tab.id === activeTab)?.content}
			</div>
		</div>
	);
}
