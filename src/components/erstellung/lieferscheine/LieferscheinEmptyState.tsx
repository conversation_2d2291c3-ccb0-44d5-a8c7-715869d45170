import { Button } from "@/components/_shared/Button"; // Import Button for actions
import { EmptyState } from "@/components/layout/EmptyState"; // Use generic EmptyState
import { FileText, Search } from "lucide-react";

interface LieferscheinEmptyStateProps {
	searchTerm: string;
	filterStatus: string;
	filterZeitraum: string;
	filterKundeId?: string;
	onResetFilters: () => void;
}

export function LieferscheinEmptyState({
	searchTerm,
	filterStatus,
	filterZeitraum,
	filterKundeId = "all",
	onResetFilters,
}: LieferscheinEmptyStateProps) {
	const hasFilters =
		searchTerm !== "" ||
		filterStatus !== "all" ||
		filterZeitraum !== "all" ||
		filterKundeId !== "all";

	if (hasFilters) {
		return (
			<EmptyState
				icon={<Search className="h-12 w-12" />}
				title="Keine Lieferscheine gefunden"
				message="Es wurden keine Lieferscheine gefunden, die den aktuellen Filterkriterien entsprechen. Versuchen Sie, die Filter anzupassen oder zurückzusetzen."
				actions={
					<Button variant="outline" onClick={onResetFilters} size="sm">
						Filter zurücksetzen
					</Button>
				}
			/>
		);
	}

	return (
		<EmptyState
			icon={<FileText className="h-12 w-12" />}
			title="Keine Lieferscheine vorhanden"
			message="Es wurden noch keine Lieferscheine erstellt. Erstellen Sie einen neuen Lieferschein, um Leistungen zu dokumentieren."
		/>
	);
}
