import { <PERSON>, CardHeader, <PERSON>Title } from "@/components/_shared/Card";
import React, { ReactNode } from "react";

interface StandardDataTableProps {
	title: string;
	infoSlot?: ReactNode;
	filterSlot: ReactNode;
	children: ReactNode;
}

/**
 * A standardized layout for data tables in the application.
 * Provides consistent styling for tables with header, filters and content.
 */
export function StandardDataTable({
	title,
	infoSlot,
	filterSlot,
	children,
}: StandardDataTableProps) {
	return (
		<Card className="shadow-lg border-0 overflow-hidden">
			<CardHeader className="pb-3 px-5 border-b border-gray-700/50">
				<div className="flex flex-col gap-4">
					<div className="flex flex-wrap items-center justify-between">
						<CardTitle className="text-lg font-medium">{title}</CardTitle>
						{infoSlot && (
							<div className="flex items-center gap-2 text-xs text-gray-400 bg-gray-800/70 px-3 py-1 rounded-full">
								{infoSlot}
							</div>
						)}
					</div>
					{filterSlot}
				</div>
			</CardHeader>
			{children}
		</Card>
	);
}
