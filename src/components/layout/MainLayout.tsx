import React from "react"; // Import React for type React.PropsWithChildren
import { Outlet } from "react-router-dom";
import { Navigation } from "../navigation/Navigation";

export function MainLayout({ children }: React.PropsWithChildren) {
	return (
		<div className="min-h-screen flex flex-col bg-gradient-to-b from-gray-900 to-gray-950 text-gray-100">
			<Navigation />
			<main className="flex-grow container mx-auto px-5 py-6 max-w-7xl">
				{children ? children : <Outlet />}{" "}
				{/* Render children if passed, otherwise Outlet for nested routes */}
			</main>
		</div>
	);
}

export default MainLayout;
