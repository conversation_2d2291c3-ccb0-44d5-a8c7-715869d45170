import React, { ReactNode } from "react";

interface EmptyStateProps {
	icon?: ReactNode;
	title: string;
	message?: string;
	actions?: ReactNode;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
	icon,
	title,
	message,
	actions,
}) => {
	return (
		<div className="flex flex-col items-center justify-center text-center p-8">
			{icon && <div className="mb-4 text-4xl text-gray-400">{icon}</div>}
			<h2 className="text-xl font-semibold text-gray-700 mb-2">{title}</h2>
			{message && <p className="text-gray-500 mb-4">{message}</p>}
			{actions && <div className="mt-2">{actions}</div>}
		</div>
	);
};
