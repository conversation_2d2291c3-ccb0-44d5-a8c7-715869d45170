import type { ReactNode } from "react";
import React from "react"; // Import React for forwardRef

interface PageLayoutProps {
	children: ReactNode;
	title: string;
	subtitle?: string;
	action?: ReactNode;
}

// Wrap PageLayout with React.forwardRef
export const PageLayout = React.forwardRef<
	HTMLDivElement, // Type of the element the ref will point to
	PageLayoutProps
>(({ children, title, subtitle, action }, ref) => {
	return (
		// Attach the ref to the main div of the PageLayout
		<div ref={ref} className="space-y-6">
			<div className="flex justify-between items-center pb-4 mb-2 border-b border-gray-800">
				<div>
					<h1 className="text-2xl font-semibold tracking-tight">{title}</h1>
					{subtitle && <p className="mt-1 text-sm text-gray-400">{subtitle}</p>}
				</div>
				{action && <div>{action}</div>}
			</div>
			{children}
		</div>
	);
});

PageLayout.displayName = "PageLayout"; // Optional: for better debugging names

export default PageLayout;
