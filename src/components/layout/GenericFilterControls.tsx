import { Button } from "@/components/_shared/Button";
import { Input } from "@/components/_shared/Input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/_shared/select";
import { cn } from "@/lib/utils/cn";
import { RotateCcw, Search, X } from "lucide-react";
import React from "react";

export type FilterOption = {
	value: string;
	label: string;
	disabled?: boolean;
};

export type SearchFilterConfig = {
	type: "search";
	id: string;
	placeholder?: string;
	value: string;
	onChange: (value: string) => void;
	className?: string;
	inputClassName?: string;
};

export type SelectFilterConfig = {
	type: "select";
	id: string;
	label?: string;
	placeholder?: string;
	value: string;
	options: FilterOption[];
	onChange: (value: string) => void;
	triggerClassName?: string;
	contentClassName?: string;
	itemClassName?: string;
	minWidth?: string;
	disabled?: boolean;
};

export type DateRangeFilterConfig = {
	type: "dateRange";
	idPrefix: string;
	zeitraumValue: string;
	startDateValue: string;
	endDateValue: string;
	onZeitraumChange: (value: string) => void;
	onStartDateChange: (value: string) => void;
	onEndDateChange: (value: string) => void;
	zeitraumOptions: FilterOption[];
	customDateInputClassName?: string;
	selectTriggerClassName?: string;
	minWidth?: string;
	disabled?: boolean;
};

export type CustomFilterConfig = {
	type: "custom";
	id: string;
	node: React.ReactNode;
};

export type FilterConfig =
	| SearchFilterConfig
	| SelectFilterConfig
	| DateRangeFilterConfig
	| CustomFilterConfig;

export interface GenericFilterControlsProps {
	filters: FilterConfig[];
	onResetAll?: () => void;
	resetButtonText?: string;
	className?: string;
	globalFilterContainerClassName?: string;
}

export function GenericFilterControls({
	filters,
	onResetAll,
	resetButtonText = "Zurücksetzen",
	className = "flex flex-wrap items-center gap-2",
	globalFilterContainerClassName,
}: GenericFilterControlsProps) {
	return (
		<div className={cn(globalFilterContainerClassName)}>
			<div className={cn(className)}>
				{filters.map((filter) => {
					if (filter.type === "search") {
						return (
							<div
								key={filter.id}
								className={cn("relative flex-grow", filter.className)}
							>
								<Search className="absolute left-2.5 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-gray-400" />
								<Input
									id={filter.id}
									placeholder={filter.placeholder || "Suchen..."}
									value={filter.value}
									onChange={(e) => filter.onChange(e.target.value)}
									className={cn(
										"h-8 text-xs pl-8 w-full bg-gray-800 border-gray-700",
										filter.inputClassName,
									)}
								/>
								{filter.value && (
									<Button
										variant="ghost"
										size="icon"
										className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 text-gray-400 hover:text-white"
										onClick={() => filter.onChange("")}
										aria-label="Suche zurücksetzen"
									>
										<X className="h-3.5 w-3.5" />
									</Button>
								)}
							</div>
						);
					}
					if (filter.type === "select") {
						return (
							<div key={filter.id} className="flex-shrink-0">
								{filter.label && (
									<label htmlFor={filter.id} className="sr-only">
										{filter.label}
									</label>
								)}
								<Select
									value={filter.value}
									onValueChange={filter.onChange}
									disabled={filter.disabled}
								>
									<SelectTrigger
										id={filter.id}
										className={cn(
											"h-8 text-xs bg-gray-800 border-gray-700",
											filter.triggerClassName,
										)}
										style={{ minWidth: filter.minWidth }}
										aria-label={filter.label || filter.placeholder}
									>
										<SelectValue placeholder={filter.placeholder} />
									</SelectTrigger>
									<SelectContent className={filter.contentClassName}>
										{filter.options.map((option) => (
											<SelectItem
												key={option.value}
												value={option.value}
												disabled={option.disabled}
												className={filter.itemClassName}
											>
												{option.label}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>
						);
					}
					if (filter.type === "dateRange") {
						return (
							<React.Fragment key={filter.idPrefix}>
								<div className="flex-shrink-0">
									<Select
										value={filter.zeitraumValue}
										onValueChange={filter.onZeitraumChange}
										disabled={filter.disabled}
									>
										<SelectTrigger
											className={cn(
												"h-8 text-xs bg-gray-800 border-gray-700",
												filter.selectTriggerClassName,
											)}
											style={{ minWidth: filter.minWidth }}
											aria-label="Zeitraum auswählen"
										>
											<SelectValue />
										</SelectTrigger>
										<SelectContent>
											{filter.zeitraumOptions.map((option) => (
												<SelectItem
													key={option.value}
													value={option.value}
													disabled={option.disabled}
												>
													{option.label}
												</SelectItem>
											))}
										</SelectContent>
									</Select>
								</div>
								{filter.zeitraumValue === "custom" && !filter.disabled && (
									<div className="flex items-center gap-1 flex-shrink-0">
										<Input
											type="date"
											value={filter.startDateValue}
											onChange={(e) => filter.onStartDateChange(e.target.value)}
											className={cn(
												"h-8 text-xs w-auto bg-gray-800 border-gray-700",
												filter.customDateInputClassName,
											)}
											title="Startdatum"
											aria-label="Startdatum"
										/>
										<span className="text-xs text-gray-400">-</span>
										<Input
											type="date"
											value={filter.endDateValue}
											onChange={(e) => filter.onEndDateChange(e.target.value)}
											className={cn(
												"h-8 text-xs w-auto bg-gray-800 border-gray-700",
												filter.customDateInputClassName,
											)}
											title="Enddatum"
											aria-label="Enddatum"
										/>
									</div>
								)}
							</React.Fragment>
						);
					}
					if (filter.type === "custom") {
						return (
							<React.Fragment key={filter.id}>{filter.node}</React.Fragment>
						);
					}
					return null;
				})}
				{onResetAll && (
					<div className="flex-shrink-0">
						<Button
							variant="outline"
							size="sm"
							onClick={onResetAll}
							className="h-8 text-xs bg-gray-800 border-gray-700 hover:bg-gray-700"
							aria-label="Alle Filter zurücksetzen"
						>
							<RotateCcw className="mr-1.5 h-3.5 w-3.5" />
							{resetButtonText}
						</Button>
					</div>
				)}
			</div>
		</div>
	);
}
