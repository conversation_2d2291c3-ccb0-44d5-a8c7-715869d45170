import { Button } from "@/components/_shared/Button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from "@/components/_shared/Dialog";
import { ReactNode } from "react";

interface AppDialogLayoutProps {
	isOpen: boolean;
	onClose: () => void;
	title: string;
	description?: string;
	icon?: ReactNode;
	children: ReactNode;
	footer?: ReactNode;
	footerAction?: {
		label: string;
		onClick: () => void;
		icon?: ReactNode;
		disabled?: boolean;
		loading?: boolean; // Added from LeistungDialogLayout
	};
	maxWidth?: "sm" | "md" | "lg" | "xl" | "2xl" | "3xl" | "4xl" | "5xl";
}

export function AppDialogLayout({
	isOpen,
	onClose,
	title,
	description,
	icon,
	children,
	footer,
	footerAction,
	maxWidth = "md",
}: AppDialogLayoutProps) {
	const maxWidthClasses = {
		sm: "w-[95vw] max-w-[95vw] sm:max-w-[500px]",
		md: "w-[95vw] max-w-[95vw] sm:max-w-[600px]",
		lg: "w-[95vw] max-w-[95vw] sm:max-w-[700px]",
		xl: "w-[95vw] max-w-[95vw] sm:max-w-[800px]",
		"2xl": "w-[95vw] max-w-[95vw] sm:max-w-[900px]",
		"3xl": "w-[95vw] max-w-[95vw] sm:max-w-[1000px]",
		"4xl": "w-[95vw] max-w-[95vw] sm:max-w-[1200px]",
		"5xl": "w-[95vw] max-w-[95vw] sm:max-w-[1400px]",
	};

	return (
		<Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
			<DialogContent
				className={`${maxWidthClasses[maxWidth]} p-0 overflow-hidden max-h-[95vh] flex flex-col`}
			>
				<DialogHeader className="p-4 pb-1 flex-shrink-0">
					<DialogTitle className="text-base flex items-center gap-1.5">
						{icon}
						{title}
					</DialogTitle>
					{description && (
						<DialogDescription className="text-xs">
							{description}
						</DialogDescription>
					)}
				</DialogHeader>

				<div className="px-4 py-2 flex-1 overflow-y-auto">{children}</div>

				<div className="flex justify-between items-center p-3 bg-gray-900 border-t border-gray-800 gap-2 flex-shrink-0">
					{footer ? (
						footer
					) : (
						<>
							<div></div>
							<div className="flex gap-2">
								<Button
									variant="outline"
									onClick={onClose}
									size="sm"
									className="h-8 text-xs"
								>
									Abbrechen
								</Button>
								{footerAction && (
									<Button
										onClick={footerAction.onClick}
										disabled={footerAction.disabled || footerAction.loading}
										size="sm"
										className="gap-1 h-8 text-xs"
									>
										{footerAction.loading ? (
											"Wird verarbeitet..."
										) : (
											<>
												{footerAction.icon}
												{footerAction.label}
											</>
										)}
									</Button>
								)}
							</div>
						</>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
}
