import {
	endOfMonth,
	endOfQuarter,
	endOfYear,
	format,
	startOfMonth,
	startOfQuarter,
	startOfYear,
	subMonths,
	subQuarters,
	subYears,
} from "date-fns";

/**
 * Formatiert einen Zeitstempel als relative Zeit (z.B. "vor 5 Minuten")
 */
export function formatRelativeTime(timestamp: number): string {
	const now = Date.now();
	const diffInSeconds = Math.floor((now - timestamp) / 1000);

	// Weniger als eine Minute
	if (diffInSeconds < 60) {
		return "gerade eben";
	}

	// Weniger als eine Stunde
	if (diffInSeconds < 3600) {
		const minutes = Math.floor(diffInSeconds / 60);
		return `vor ${minutes} ${minutes === 1 ? "Minute" : "Minuten"}`;
	}

	// Weniger als ein Tag
	if (diffInSeconds < 86400) {
		const hours = Math.floor(diffInSeconds / 3600);
		return `vor ${hours} ${hours === 1 ? "Stunde" : "Stunden"}`;
	}

	// Weniger als eine Woche
	if (diffInSeconds < 604800) {
		const days = Math.floor(diffInSeconds / 86400);
		return `vor ${days} ${days === 1 ? "Tag" : "Tagen"}`;
	}

	// Weniger als ein Monat
	if (diffInSeconds < 2592000) {
		const weeks = Math.floor(diffInSeconds / 604800);
		return `vor ${weeks} ${weeks === 1 ? "Woche" : "Wochen"}`;
	}

	// Weniger als ein Jahr
	if (diffInSeconds < 31536000) {
		const months = Math.floor(diffInSeconds / 2592000);
		return `vor ${months} ${months === 1 ? "Monat" : "Monaten"}`;
	}

	// Mehr als ein Jahr
	const years = Math.floor(diffInSeconds / 31536000);
	return `vor ${years} ${years === 1 ? "Jahr" : "Jahren"}`;
}

/**
 * Berechnet Datumsbereich basierend auf vordefinierten Optionen
 * Verwendet date-fns für präzise Datumsberechnungen
 */
export type DateRangeOption =
	| "all"
	| "month"
	| "lastMonth"
	| "quarter"
	| "lastQuarter"
	| "year"
	| "lastYear"
	| "custom"
	| "thisMonth"
	| "thisQuarter"
	| "thisYear"
	| "last7"
	| "last30";

export interface DateRange {
	startDate: Date;
	endDate: Date;
}

export function calculateDateRange(option: DateRangeOption): DateRange {
	const today = new Date();

	switch (option) {
		case "month":
		case "thisMonth": {
			// Erster Tag des aktuellen Monats bis letzter Tag des aktuellen Monats
			return {
				startDate: startOfMonth(today),
				endDate: endOfMonth(today),
			};
		}
		case "lastMonth": {
			// Erster Tag des letzten Monats bis letzter Tag des letzten Monats
			const lastMonth = subMonths(today, 1);
			return {
				startDate: startOfMonth(lastMonth),
				endDate: endOfMonth(lastMonth),
			};
		}
		case "quarter":
		case "thisQuarter": {
			// Erster Tag des aktuellen Quartals bis letzter Tag des aktuellen Quartals
			return {
				startDate: startOfQuarter(today),
				endDate: endOfQuarter(today),
			};
		}
		case "lastQuarter": {
			// Erster Tag des letzten Quartals bis letzter Tag des letzten Quartals
			const lastQuarter = subQuarters(today, 1);
			return {
				startDate: startOfQuarter(lastQuarter),
				endDate: endOfQuarter(lastQuarter),
			};
		}
		case "year":
		case "thisYear": {
			// Erster Tag des aktuellen Jahres bis letzter Tag des aktuellen Jahres
			return {
				startDate: startOfYear(today),
				endDate: endOfYear(today),
			};
		}
		case "lastYear": {
			// Erster Tag des letzten Jahres bis letzter Tag des letzten Jahres
			const lastYear = subYears(today, 1);
			return {
				startDate: startOfYear(lastYear),
				endDate: endOfYear(lastYear),
			};
		}
		case "last7": {
			// Letzte 7 Tage
			const startDate = new Date();
			startDate.setDate(today.getDate() - 7);
			startDate.setHours(0, 0, 0, 0);
			return {
				startDate,
				endDate: today,
			};
		}
		case "last30": {
			// Letzte 30 Tage
			const startDate = new Date();
			startDate.setDate(today.getDate() - 30);
			startDate.setHours(0, 0, 0, 0);
			return {
				startDate,
				endDate: today,
			};
		}
		case "all":
		default: {
			// Für "all" oder unbekannte Optionen: sehr früher Start bis heute
			return {
				startDate: new Date(0), // 1970-01-01
				endDate: today,
			};
		}
	}
}

/**
 * Konvertiert ein Date-Objekt in ein ISO-Datums-String (YYYY-MM-DD)
 */
export function toISODateString(date: Date): string {
	return date.toISOString().split("T")[0];
}

/**
 * Formatiert ein Datum in deutsches Format (DD.MM.YYYY)
 */
export function formatDateDE(date: Date): string {
	return format(date, "dd.MM.yyyy");
}

/**
 * Formatiert einen Datumsbereich für die Anzeige im Format "DD.MM.YYYY - DD.MM.YYYY"
 * Stellt sicher, dass das Enddatum korrekt als letzter Tag des Zeitraums angezeigt wird
 */
export function formatDateRangeDE(
	startDateStr: string,
	endDateStr: string,
): string {
	// Für das Startdatum verwenden wir das normale Datum
	const startDate = new Date(startDateStr);

	// Für das Enddatum prüfen wir, ob es sich um einen Monatsende handelt
	// und korrigieren es gegebenenfalls
	const endDate = new Date(endDateStr);

	// The specific correction for 30th/31st day of month has been removed.
	// The function will now format the dates as they are provided.
	// If end-of-month logic is required, it should be applied when the endDateStr is generated.

	return `${formatDateDE(startDate)} - ${formatDateDE(endDate)}`;
}

// ===== TERMINE-SPEZIFISCHE FUNKTIONEN =====

/**
 * Berechnet die Restlaufzeit bis zu einem Datum mit verbesserter Vergangenheits-Anzeige
 * @param targetDate - Zieldatum als ISO-String (YYYY-MM-DD)
 * @param targetTime - Optionale Uhrzeit als String (HH:MM)
 * @returns Formatierte Restlaufzeit-String
 */
export function calculateTimeRemaining(
	targetDate?: string,
	targetTime?: string,
): string {
	if (!targetDate) return "-";

	const now = new Date();

	// Erstelle Zieldatum mit optionaler Uhrzeit
	let target: Date;
	if (targetTime) {
		const [hours, minutes] = targetTime.split(":").map(Number);
		target = new Date(targetDate);
		target.setHours(hours, minutes, 0, 0);
	} else {
		target = new Date(targetDate);
		target.setHours(23, 59, 59, 999); // Ende des Tages
	}

	const diffMs = target.getTime() - now.getTime();

	// Wenn das Datum in der Vergangenheit liegt - zeige relative Zeit
	if (diffMs < 0) {
		const pastMs = Math.abs(diffMs);
		const pastMinutes = Math.floor(pastMs / (1000 * 60));
		const pastHours = Math.floor(pastMs / (1000 * 60 * 60));
		const pastDays = Math.floor(pastMs / (1000 * 60 * 60 * 24));

		if (pastDays > 0) {
			return `vor ${pastDays} ${pastDays === 1 ? "Tag" : "Tagen"}`;
		} else if (pastHours > 0) {
			return `vor ${pastHours} ${pastHours === 1 ? "Stunde" : "Stunden"}`;
		} else {
			return `vor ${pastMinutes} ${pastMinutes === 1 ? "Minute" : "Minuten"}`;
		}
	}

	const diffMinutes = Math.floor(diffMs / (1000 * 60));
	const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
	const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

	// Wenn mehr als 1 Tag: Zeige nur Tage
	if (diffDays > 1) {
		return `${diffDays} Tage`;
	}

	// Wenn 1 Tag oder weniger aber mehr als 2 Stunden: Zeige Stunden und Minuten
	if (diffHours >= 2) {
		const remainingMinutes = diffMinutes % 60;
		return `${diffHours}h ${remainingMinutes}min`;
	}

	// Wenn weniger als 2 Stunden: Zeige nur Minuten
	return `${diffMinutes} min`;
}

/**
 * Formatiert ein Datum für die Anzeige
 * @param dateString - ISO-Datum-String
 * @returns Formatiertes Datum (DD.MM.YYYY)
 */
export function formatDate(dateString?: string): string {
	if (!dateString) return "-";
	const date = new Date(dateString);
	return date.toLocaleDateString("de-DE", {
		day: "2-digit",
		month: "2-digit",
		year: "numeric",
	});
}

/**
 * Formatiert eine Uhrzeit für die Anzeige
 * @param timeString - Uhrzeit-String (HH:MM)
 * @returns Formatierte Uhrzeit oder "-"
 */
export function formatTime(timeString?: string): string {
	if (!timeString) return "-";
	return timeString;
}

/**
 * Prüft ob ein Datum heute ist
 * @param dateString - ISO-Datum-String
 * @returns true wenn heute
 */
export function isToday(dateString: string): boolean {
	const today = new Date().toISOString().split("T")[0];
	return dateString === today;
}

/**
 * Prüft ob ein Datum in der Vergangenheit liegt
 * @param dateString - ISO-Datum-String
 * @param timeString - Optionale Uhrzeit
 * @returns true wenn in der Vergangenheit
 */
export function isPast(dateString: string, timeString?: string): boolean {
	const now = new Date();
	let target: Date;

	if (timeString) {
		const [hours, minutes] = timeString.split(":").map(Number);
		target = new Date(dateString);
		target.setHours(hours, minutes, 0, 0);
	} else {
		target = new Date(dateString);
		target.setHours(23, 59, 59, 999);
	}

	return target.getTime() < now.getTime();
}
