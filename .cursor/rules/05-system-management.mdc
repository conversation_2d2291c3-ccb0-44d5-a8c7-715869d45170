---
description: System-Management, Feedback, Standards, E-Mail und Mitarbeiter
globs: **/system/**, **/verwaltung/**, **/feedback/**
alwaysApply: false
---
# System-Management

Zentrale Verwaltung von systemweiten Einstellungen, Feedback und Mitarbeitern.

## Feedback-System

### Workflow
- **Sammlung**: Über Feedback-Button in der Navigation (gelbes Ausrufezeichen)
- **Kategorisierung**: "feature" oder "bug" Requests
- **Status-Tracking**: "offen", "in_bearbeitung", "erledigt"
- **Verwaltung**: Zentrale Verwaltungsseite unter `/system/feedback`

## Standards-System

### Zweck
Zentrale Anzeige von systemweiten Standardeinstellungen für PDF-Dokumente.

### UI-Organisation (Route: `/system/standards`)
- **E-Mail Tab**: Allgemeine E-Mail-Konfiguration (Resend API-Einstellungen)
- **Lieferscheine Tab**:
  - Logo-Anzeige und PDF-Einstellungen
  - E-Mail-Einstellungen für Lieferscheine
  - E-Mail-Template für Lieferscheine
- **Übersichten Tab**:
  - Logo-Anzeige und PDF-Einstellungen
  - E-Mail-Einstellungen für Übersichten
  - E-Mail-Template für Übersichten

### Konfiguration
- **Feature-spezifische Configs**: Daten werden aus `lieferscheineConfig.ts` und `uebersichtenConfig.ts` geladen
- **Logo-Verwaltung**: Upload und Speicherung über Convex Storage

## E-Mail-System

### Resend-Integration (Convex Component)
- **Convex Resend Component**: Zuverlässiger E-Mail-Versand mit Queueing und Retry-Logik
- **PDF-Anhänge**: Clientseitige PDF-Generierung mit React-PDF und direkter Resend API
- **Empfänger-Management**: Automatische CC-Listen mit Mitarbeitern
- **Webhook-Integration**: Automatische Status-Updates für Zustellung, Bounces und Beschwerden

### Anrede-Logik
- **Hauptansprechpartner**: Konsistente Anrede basierend auf Kundenstammdaten
- **Fallback**: Generische Anrede bei fehlendem Hauptansprechpartner

### Features der Resend-Integration
- **Queueing**: E-Mails werden zuverlässig in eine Warteschlange eingereiht
- **Batching**: Automatisches Batching für große E-Mail-Mengen
- **Durable Execution**: Garantierte Zustellung auch bei temporären Ausfällen
- **Idempotenz**: Verhindert doppelte E-Mails durch automatische Idempotenz-Keys
- **Rate Limiting**: Respektiert Resend API-Limits
- **Status-Tracking**: Verfolgen Sie E-Mail-Status (Zugestellt, Bounce, Spam-Beschwerde)

### Logging-System
Kompakte, strukturierte Logs für E-Mail-Versand:

```
[EMAIL] 📧 Lieferschein "LS-2024-001" | User: <EMAIL> | Recipients: Max Mustermann <<EMAIL>>, Anna Schmidt <<EMAIL>> | Email IDs: 12345, 67890

[EMAIL] 📊 Übersicht "Kunde1 (31.05.2025 - 29.06.2025)" | User: <EMAIL> | Recipients: Sven Labitzki <<EMAIL>> | Email IDs: 11111

[EMAIL] ❌ Lieferschein "LS-2024-002" | User: <EMAIL> | Error: E-Mail-Konfiguration unvollständig
```

**Log-Informationen:**
- **Wer**: Clerk User Email (triggering user)
- **Was**: Dokumentnummer/-bezeichnung
- **An wen**: Vollständige Liste aller Empfänger-E-Mail-Adressen
- **Email IDs**: Resend E-Mail-IDs für Status-Tracking
- **Fehler**: Kompakte Fehlermeldung (falls vorhanden)

## Mitarbeiter-Verwaltung

### Zweck
- **E-Mail-Integration**: Automatische CC-Listen für Dokument-Versand
- **Leistungserfassung**: Zuordnung von Mitarbeitern zu Leistungen
- **Interne Verwaltung**: Zentrale Mitarbeiterdatenbank

### API-Struktur
- **Basis**: `api.verwaltung.mitarbeiter.*`
- **Frontend**: `/verwaltung/mitarbeiter/`

### Datenmodell
```typescript
{
  name: string,
  email: string
}
```

## Doku-Kategorien-System

### Konfigurationsbasiert
- **Definition**: Kategorien in `convex/system/dokuKategorienConfig.ts`
- **Synchronisation**: Automatische Datenbank-Updates via `initializeDefaults`
- **Felder-Typen**: Text, Password, URL, Number, etc.
- **Validierung**: Pflichtfelder und Typen-Validierung