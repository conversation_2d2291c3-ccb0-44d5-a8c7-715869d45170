---
description: auth und routing
globs:
alwaysApply: false
---
# Authentifizierung und Routing

Die Anwendung verwendet [Clerk](mdc:https:/clerk.com) für die Benutzerauthentifizierung in Verbindung mit React Router für das Routing und Convex als Backend-Lösung.

## Authentifizierungs-Setup

### Frontend-Setup

- Die Clerk-Integration wird in [`src/main.tsx`](mdc:src/main.tsx) initialisiert:
  ```tsx
  <ClerkProvider publishableKey={import.meta.env.VITE_CLERK_PUBLISHABLE_KEY}>
    <ConvexProviderWithClerk client={convex} useAuth={useAuth}>
      <App />
    </ConvexProviderWithClerk>
  </ClerkProvider>
  ```
- Die `VITE_CLERK_PUBLISHABLE_KEY` Umgebungsvariable muss in `.env.local` gesetzt sein.

### Backend-Setup

- Convex ist über [`convex/auth.config.ts`](mdc:convex/auth.config.ts) für die Verwendung mit Clerk konfiguriert:
  ```typescript
  export default {
    providers: [
      {
        domain: process.env.CLERK_DOMAIN,
        applicationID: "convex",
      },
    ],
  };
  ```
- Die `CLERK_DOMAIN` Umgebungsvariable muss im Convex Dashboard konfiguriert werden (ohne `VITE_`-Präfix).
- URL-Format: `https://your-clerk-instance.clerk.accounts.dev/`

## Routing-Struktur

Die Routing-Struktur ist in [`src/App.tsx`](mdc:src/App.tsx) definiert und folgt der Navigationsstruktur der Anwendung:

```tsx
<BrowserRouter>
  <Routes>
    {/* Public route for signing in */}
    <Route path="/signin" element={<SignInPage />} />

    {/* Routes that require authentication */}
    <Route
      path="/*" // This will match any path other than /signin if not caught by more specific routes
      element={isSignedIn ? <ProtectedAppLayout /> : <RedirectToSignIn />}
    >
      {/* Default route for "/" when signed in */}
      <Route
        index
        element={<DashboardPage />}
      />

      {/* Kunden Routes */}
      <Route
        path="kunden"
        element={<Navigate to="/kunden/stammdaten" replace />}
      />
      <Route path="kunden/stammdaten" element={<KundenPage />} />
      <Route path="kunden/kontingente" element={<KontingentePage />} />
      <Route path="kunden/doku" element={<KundenDokuPage />} />
      <Route path="kunden/doku/:id" element={<KundenDokuDetailPage />} />

      {/* Erstellung Routes */}
      <Route
        path="erstellung"
        element={<Navigate to="/erstellung/leistung" replace />}
      />
      <Route path="erstellung/leistung" element={<LeistungenPage />} />
      <Route path="erstellung/uebersicht" element={<UebersichtPage />} />
      <Route path="erstellung/lieferschein" element={<LieferscheinPage />} />
      <Route path="erstellung/lieferschein/:id" element={<LieferscheinDetailPage />} />

      {/* Verwaltung Routes */}
      <Route
        path="verwaltung"
        element={<Navigate to="/verwaltung/mitarbeiter" replace />}
      />
      <Route path="verwaltung/mitarbeiter" element={<MitarbeiterPage />} />

      {/* System Routes */}
      <Route
        path="system"
        element={<Navigate to="/system/feedback" replace />}
      />
      <Route path="system/feedback" element={<FeedbackPage />} />
      <Route path="system/standards" element={<StandardsPage />} />
      <Route
        path="system/doku-kategorien"
        element={<DokuKategorienPage />}
      />

      {/* Catch-all for any other authenticated paths not matched above */}
      {/* This effectively means any path like /foo/bar if signed in will redirect to dashboard */}
      <Route
        path="*"
        element={<Navigate to="/" replace />}
      />
    </Route>
  </Routes>
</BrowserRouter>
```

## Sicherheitskonzept

Die Anwendung verwendet ein mehrstufiges Sicherheitskonzept:

1. **Öffentliche Routen**: Nur die `/signin`-Route ist öffentlich zugänglich.

2. **Geschützte Routen**: Alle anderen Routen (`/*`) sind durch Clerk's `<SignedIn>` und `<SignedOut>` Komponenten geschützt:
   - `<SignedIn>`: Zeigt den Inhalt nur an, wenn ein Benutzer angemeldet ist
   - `<SignedOut>`: Leitet automatisch zur Anmeldeseite weiter, wenn kein Benutzer angemeldet ist

3. **Fallback-Routen**:
   - Die Startseite (`/`) leitet zum Dashboard weiter
   - Nicht existierende Routen (`*`) leiten ebenfalls zum Dashboard weiter

4. **Backend-Sicherheit**: Alle Convex-Funktionen (Queries und Mutations) prüfen die Authentifizierung mit:
   ```typescript
   const identity = await ctx.auth.getUserIdentity();
   if (!identity) {
     throw new Error("Nicht authentifiziert.");
   }
   ```

5. **Authentifizierungs-Validierung**:
   - Ein Script (`scripts/check-auth.js`) kann verwendet werden, um zu überprüfen, ob alle öffentlichen Convex-Funktionen eine Authentifizierungsprüfung enthalten.
   - Für die lokale Entwicklung können bestimmte Funktionen bedingungsbasiert ohne Authentifizierung ausgeführt werden:
   ```typescript
   if (!identity && !process.env.CONVEX_CLOUD_URL) {
     console.warn("Ausführung ohne Authentifizierung (nur für Entwicklung)");
   } else if (!identity && process.env.CONVEX_CLOUD_URL) {
     throw new Error("Nicht authentifiziert.");
   }
   ```

## Implementierungsdetails

### Routing-Komponenten

#### ProtectedAppLayout

Der `ProtectedAppLayout` ist die Hauptkomponente für alle geschützten Routen:

```tsx
export function ProtectedAppLayout() {
  return (
    <div className="min-h-screen flex flex-col bg-background text-foreground">
      <Navigation />
      <main className="flex-1 px-4 py-6 md:px-6 md:py-8">
        <Outlet />
      </main>
      <footer className="py-4 px-6 text-center text-muted-foreground text-xs border-t border-border">
        <p>© {new Date().getFullYear()} innov8-IT. Alle Rechte vorbehalten.</p>
      </footer>
    </div>
  );
}
```

- Enthält die `Navigation`-Komponente für den Header
- Verwendet `Outlet` von React Router, um den Inhalt der jeweiligen Route anzuzeigen
- Enthält einen Footer mit Copyright-Hinweis

### Benutzerinformationen und Aktionen im Frontend

- Der `useUser`-Hook von `@clerk/clerk-react` wird verwendet, um Informationen über den eingeloggten Benutzer abzurufen.
- Der `useClerk`-Hook stellt Funktionen wie `openUserProfile` bereit, um das Clerk-Profilmodal zu öffnen.
- Der [`SignOutButton.tsx`](mdc:src/components/_auth/SignOutButton.tsx) wird für den Logout verwendet.

### Nutzung von Shadcn UI-Komponenten und Tailwind CSS

- Die Anwendung verwendet Shadcn UI-Komponenten, die auf Tailwind CSS basieren.
- Die Styling-Variablen sind in `src/index.css` mit einem Dunkelmodus-Theme definiert.
- Die Klassenbezeichnungen folgen dem Shadcn UI-Konventionen, z.B.:
  - `bg-background`: Hintergrundfarbe
  - `text-foreground`: Textfarbe
  - `border-border`: Rahmenfarbe
  - `text-muted-foreground`: Gedämpfte Textfarbe
