---
description: Leistungserfassung, Lieferscheine und Übersichten
globs: **/erstellung/**
alwaysApply: false
---
# Erstellungs-Systeme

Systeme für die Erfassung von Leistungen und Erstellung von Dokumenten.

## Leistungserfassungs-System

### Kernkonzepte
- **Leistung**: Erbrachte Dienstleistung mit Start-/Endzeit, Beschreibung und Kostenberechnung
- **Multi-Position-Erfassung**: Mehrere Leistungspositionen in einem Dialog für einen Kunden
- **Kontingent-Verknüpfung**: Automatische Aktualisierung der verbrauchten Kontingent-Stunden
- **Kontingent-Splitting**: Automatische Aufteilung auf zwei Kontingente bei Überschreitung
- **Preis-Hierarchie**: Kundenstandards können pro Leistung überschrieben werden

### Multi-Position-Workflow
1. **Globale Kundenauswahl**: Ein Kunde für alle Positionen im Dialog
2. **Position hinzufügen**: Dynamisches Hinzufügen weiterer Leistungspositionen
3. **Kompakte UI**: Optimierte, dunkle Benutzeroberfläche mit minimalen Abständen
4. **Batch-Speicherung**: Alle Positionen werden gemeinsam gespeichert

### Kontingent-Splitting-System
- **Automatische Erkennung**: Zeigt zweites Kontingent-Feld bei Überschreitung
- **Intelligente Aufteilung**: Erste Kontingent wird maximal ausgeschöpft, Rest geht an zweites
- **Visuelle Kennzeichnung**: Split-Icon in der Zusammenfassung mit Tooltip
- **Rückgängig-Funktion**: Beim Löschen werden beide Kontingente korrekt entlastet

### Automatisierungen
- **Stundenberechnung**: Automatische Rundung auf 15-Minuten-Intervalle
- **Anfahrtskosten**: Bei Remote-Leistungen ausgegraut aber sichtbar (Wert: 0)
- **Kontingent-Updates**: Automatische Synchronisation bei CRUD-Operationen
- **Sortierung**: Lieferscheine sortieren nach Datum, dann Startzeit, dann Mitarbeitername

### Anzeige-Features
- **Kontingent-Anzeige**: Bei aufgeteilten Leistungen werden beide Kontingente im Format "Kontingent1 / Kontingent2" angezeigt
- **Icon-only Buttons**: Kompakte Bedienung mit Icons und Tooltips (Position hinzufügen, Kontingent-Split-Indikator)
- **Responsive Design**: Optimierte Darstellung für verschiedene Bildschirmgrößen

## Lieferschein-System

### Workflow
1. **Entwurf**: Erststellung ohne Nummer, beliebig bearbeitbar
2. **Leistungen hinzufügen**: Mehrfachauswahl und Zuordnung
3. **Finalisierung**: Vergabe der Lieferschein-Nummer (Format: YYXXXXX)

### Korrektur-System
- **Korrektur-Workflow**: Neue Korrektur → Bearbeitung → Finalisierung
- **Nummerierung**: Original-Nummer mit Versionssuffix (z.B. 2500001.2)
- **Referenzierung**: Korrekturen verweisen immer auf das ursprüngliche Original
- **Markierung**: Originale werden als "korrigiert" markiert

### Geschäftsregeln
- Finalisierte Lieferscheine sind unveränderlich
- Nur Originale können korrigiert werden (keine Korrektur einer Korrektur)
- Erste Korrektur erhält Version .2 (Version .1 wird übersprungen)

## Übersicht-System

### PDF-Generierung
- **Kundenauswahl**: Filter nach Kunde und Zeitraum
- **Leistungs-/Kontingent-Auswahl**: Granulare Kontrolle über Berichtsinhalte
- **Standards-Integration**: Verwendung von Logo und Firmeninformationen
- **Zusammenfassung**: Automatische Berechnung von Gesamtstunden und -kosten

## Konfigurationssystem

### Feature-spezifische Configs
- **`convex/erstellung/lieferscheineConfig.ts`**: Alle Lieferschein-bezogenen Einstellungen
  - PDF-Einstellungen (Header, Footer, Logo, Signatur, Legal Text)
  - E-Mail-Einstellungen (Standard-Versandoptionen)
  - E-Mail-Template für Lieferscheine
- **`convex/erstellung/uebersichtenConfig.ts`**: Alle Übersicht-bezogenen Einstellungen
  - PDF-Einstellungen (Header, Footer, Logo, Leistungsübersicht, etc.)
  - E-Mail-Einstellungen (Standard-Versandoptionen)
  - E-Mail-Template für Übersichten
- **`convex/system/email.ts`**: E-Mail-System mit Resend-Integration
  - Convex Resend Component Konfiguration
  - E-Mail-Versand-Funktionen mit PDF-Anhängen
  - Webhook-Handler für Status-Updates

### Organisationsprinzip
- **Feature-Trennung**: Jeder Dokumenttyp hat seine eigene Config-Datei
- **Zentrale E-Mail-Logik**: Resend-Integration in `convex/system/email.ts`
- **Konsistente Struktur**: Configs liegen im gleichen Ordner wie die zugehörige Funktionalität