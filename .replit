modules = ["nodejs-20"]

[workflows]
runButton = "DEV"

[[workflows.workflow]]
name = "DEV"
mode = "sequential"
author = 2119567

[[workflows.workflow.tasks]]
task = "packager.installForAll"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npm run dev"

[deployment]
build = ["sh", "-c", "bun run build"]
run = ["sh", "-c", "bun run start"]
deploymentTarget = "cloudrun"

[[ports]]
localPort = 5173
externalPort = 80
